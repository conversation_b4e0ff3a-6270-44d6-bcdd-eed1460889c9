{"Version": 1, "WorkspaceRootPath": "C:\\development\\OT\\OT-O2x\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\controllers\\xtrading\\operations\\strategycontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\controllers\\xtrading\\operations\\strategycontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\models\\xtrading\\derivativesconfigurations.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\models\\xtrading\\derivativesconfigurations.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\views\\xtradingbalance\\_balancecontent.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\views\\xtradingbalance\\_balancecontent.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{CFA32E12-93DC-4F0D-8FC2-B25CCEFF52BA}|Services\\xtradingbroker\\XTradingBroker.csproj|c:\\development\\ot\\ot-o2x\\services\\xtradingbroker\\extensions\\balanceaccountextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{CFA32E12-93DC-4F0D-8FC2-B25CCEFF52BA}|Services\\xtradingbroker\\XTradingBroker.csproj|solutionrelative:services\\xtradingbroker\\extensions\\balanceaccountextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\middlewares\\logging\\timermetricsmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\middlewares\\logging\\timermetricsmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\statistics\\prometheuswebcoremetrics.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\statistics\\prometheuswebcoremetrics.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\middlewares\\logging\\requestloggingmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\middlewares\\logging\\requestloggingmiddleware.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\wwwroot\\js\\ui\\instruments\\bestworst\\bestworstcontrol.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\wwwroot\\js\\ui\\instruments\\bestworst\\bestworstcontrol.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\controllers\\api\\v1\\metricscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\controllers\\api\\v1\\metricscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\wwwroot\\js\\ui\\instruments\\stocklist\\stocklistcontrol.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\wwwroot\\js\\ui\\instruments\\stocklist\\stocklistcontrol.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\controllers\\api\\v2\\ordercontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\controllers\\api\\v2\\ordercontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\controllers\\api\\v1\\chartcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\controllers\\api\\v1\\chartcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{303F0D30-22B0-4F88-A042-BE0342CE22BB}|Services\\brokerconnector\\BrokerConnector.csproj|c:\\development\\ot\\ot-o2x\\services\\brokerconnector\\services\\servicecenter.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{303F0D30-22B0-4F88-A042-BE0342CE22BB}|Services\\brokerconnector\\BrokerConnector.csproj|solutionrelative:services\\brokerconnector\\services\\servicecenter.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{DF039CF6-45DB-4B9F-B294-F81008776FE1}|Services\\virtualbroker\\VirtualBroker.csproj|c:\\development\\ot\\ot-o2x\\services\\virtualbroker\\items\\logic\\vbdata.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{DF039CF6-45DB-4B9F-B294-F81008776FE1}|Services\\virtualbroker\\VirtualBroker.csproj|solutionrelative:services\\virtualbroker\\items\\logic\\vbdata.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{DF039CF6-45DB-4B9F-B294-F81008776FE1}|Services\\virtualbroker\\VirtualBroker.csproj|c:\\development\\ot\\ot-o2x\\services\\virtualbroker\\cache\\balanceworker.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{DF039CF6-45DB-4B9F-B294-F81008776FE1}|Services\\virtualbroker\\VirtualBroker.csproj|solutionrelative:services\\virtualbroker\\cache\\balanceworker.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\wwwroot\\otwebapi\\demo.html||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\wwwroot\\otwebapi\\demo.html||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\program.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\appsettings.pro.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\appsettings.pro.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{303F0D30-22B0-4F88-A042-BE0342CE22BB}|Services\\brokerconnector\\BrokerConnector.csproj|c:\\development\\ot\\ot-o2x\\services\\brokerconnector\\extensions\\iservicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{303F0D30-22B0-4F88-A042-BE0342CE22BB}|Services\\brokerconnector\\BrokerConnector.csproj|solutionrelative:services\\brokerconnector\\extensions\\iservicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\wwwroot\\docs\\df\\dec\\class_microsoft_1_1_extensions_1_1_dependency_injection_1_1_i_service_collection_extensions-members.html||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\wwwroot\\docs\\df\\dec\\class_microsoft_1_1_extensions_1_1_dependency_injection_1_1_i_service_collection_extensions-members.html||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|c:\\development\\ot\\ot-o2x\\services\\ot-webcore\\startup.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{B80D8C97-0F2B-4AD0-938E-AA152C79C47C}|Services\\ot-webcore\\OT.WebCore.csproj|solutionrelative:services\\ot-webcore\\startup.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "StrategyController.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Controllers\\XTrading\\Operations\\StrategyController.cs", "RelativeDocumentMoniker": "Services\\ot-webcore\\Controllers\\XTrading\\Operations\\StrategyController.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Controllers\\XTrading\\Operations\\StrategyController.cs", "RelativeToolTip": "Services\\ot-webcore\\Controllers\\XTrading\\Operations\\StrategyController.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAawCUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T12:23:04.354Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "DerivativesConfigurations.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Models\\XTrading\\DerivativesConfigurations.cs", "RelativeDocumentMoniker": "Services\\ot-webcore\\Models\\XTrading\\DerivativesConfigurations.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Models\\XTrading\\DerivativesConfigurations.cs", "RelativeToolTip": "Services\\ot-webcore\\Models\\XTrading\\DerivativesConfigurations.cs", "ViewState": "AgIAAEEAAAAAAAAAAAAAAEQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T12:22:54.567Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "_BalanceContent.cshtml", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Views\\XTradingBalance\\_BalanceContent.cshtml", "RelativeDocumentMoniker": "Services\\ot-webcore\\Views\\XTradingBalance\\_BalanceContent.cshtml", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Views\\XTradingBalance\\_BalanceContent.cshtml", "RelativeToolTip": "Services\\ot-webcore\\Views\\XTradingBalance\\_BalanceContent.cshtml", "ViewState": "AgIAAL4CAAAAAAAAAIAwwMwCAAC+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-05-28T12:19:50.105Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{40ea2e6b-2121-4bb8-a43e-c83c04b51041}"}, {"$type": "Bookmark", "Name": "ST:128:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:128:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Document", "DocumentIndex": 3, "Title": "BalanceAccountExtensions.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\xtradingbroker\\Extensions\\BalanceAccountExtensions.cs", "RelativeDocumentMoniker": "Services\\xtradingbroker\\Extensions\\BalanceAccountExtensions.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\xtradingbroker\\Extensions\\BalanceAccountExtensions.cs", "RelativeToolTip": "Services\\xtradingbroker\\Extensions\\BalanceAccountExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T10:54:31.723Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "TimerMetricsMiddleware.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Middlewares\\Logging\\TimerMetricsMiddleware.cs", "RelativeDocumentMoniker": "Services\\ot-webcore\\Middlewares\\Logging\\TimerMetricsMiddleware.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Middlewares\\Logging\\TimerMetricsMiddleware.cs", "RelativeToolTip": "Services\\ot-webcore\\Middlewares\\Logging\\TimerMetricsMiddleware.cs", "ViewState": "AgIAAEgAAAAAAAAAAAAIwEoAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T10:28:19.957Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "RequestLoggingMiddleware.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Middlewares\\Logging\\RequestLoggingMiddleware.cs", "RelativeDocumentMoniker": "Services\\ot-webcore\\Middlewares\\Logging\\RequestLoggingMiddleware.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Middlewares\\Logging\\RequestLoggingMiddleware.cs", "RelativeToolTip": "Services\\ot-webcore\\Middlewares\\Logging\\RequestLoggingMiddleware.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T10:26:42.143Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "MetricsController.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Controllers\\Api\\v1\\MetricsController.cs", "RelativeDocumentMoniker": "Services\\ot-webcore\\Controllers\\Api\\v1\\MetricsController.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Controllers\\Api\\v1\\MetricsController.cs", "RelativeToolTip": "Services\\ot-webcore\\Controllers\\Api\\v1\\MetricsController.cs", "ViewState": "AgIAABYAAAAAAAAAAADgvx4AAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T10:21:26.422Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "stocklistcontrol.js", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\wwwroot\\js\\ui\\instruments\\stocklist\\stocklistcontrol.js", "RelativeDocumentMoniker": "Services\\ot-webcore\\wwwroot\\js\\ui\\instruments\\stocklist\\stocklistcontrol.js", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\wwwroot\\js\\ui\\instruments\\stocklist\\stocklistcontrol.js", "RelativeToolTip": "Services\\ot-webcore\\wwwroot\\js\\ui\\instruments\\stocklist\\stocklistcontrol.js", "ViewState": "AgIAABwAAAAAAAAAAAAAAAQAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-28T10:20:37.749Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "bestworstcontrol.js", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\wwwroot\\js\\ui\\instruments\\bestworst\\bestworstcontrol.js", "RelativeDocumentMoniker": "Services\\ot-webcore\\wwwroot\\js\\ui\\instruments\\bestworst\\bestworstcontrol.js", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\wwwroot\\js\\ui\\instruments\\bestworst\\bestworstcontrol.js", "RelativeToolTip": "Services\\ot-webcore\\wwwroot\\js\\ui\\instruments\\bestworst\\bestworstcontrol.js", "ViewState": "AgIAAFoAAAAAAAAAAAAcwGgAAABnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-05-28T10:11:34.099Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "PrometheusWebCoreMetrics.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Statistics\\PrometheusWebCoreMetrics.cs", "RelativeDocumentMoniker": "Services\\ot-webcore\\Statistics\\PrometheusWebCoreMetrics.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Statistics\\PrometheusWebCoreMetrics.cs", "RelativeToolTip": "Services\\ot-webcore\\Statistics\\PrometheusWebCoreMetrics.cs", "ViewState": "AgIAACoAAAAAAAAAAAASwDoAAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T10:11:16.389Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "OrderController.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Controllers\\Api\\v2\\OrderController.cs", "RelativeDocumentMoniker": "Services\\ot-webcore\\Controllers\\Api\\v2\\OrderController.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Controllers\\Api\\v2\\OrderController.cs", "RelativeToolTip": "Services\\ot-webcore\\Controllers\\Api\\v2\\OrderController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T13:43:34.061Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "ServiceCenter.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\brokerconnector\\Services\\ServiceCenter.cs", "RelativeDocumentMoniker": "Services\\brokerconnector\\Services\\ServiceCenter.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\brokerconnector\\Services\\ServiceCenter.cs", "RelativeToolTip": "Services\\brokerconnector\\Services\\ServiceCenter.cs", "ViewState": "AgIAAIoAAAAAAAAAAAAvwJwAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T13:43:16.269Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "VBData.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\virtualbroker\\Items\\Logic\\VBData.cs", "RelativeDocumentMoniker": "Services\\virtualbroker\\Items\\Logic\\VBData.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\virtualbroker\\Items\\Logic\\VBData.cs", "RelativeToolTip": "Services\\virtualbroker\\Items\\Logic\\VBData.cs", "ViewState": "AgIAAPgBAAAAAAAAAAAnwP8BAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T13:37:53.977Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "BalanceWorker.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\virtualbroker\\Cache\\BalanceWorker.cs", "RelativeDocumentMoniker": "Services\\virtualbroker\\Cache\\BalanceWorker.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\virtualbroker\\Cache\\BalanceWorker.cs", "RelativeToolTip": "Services\\virtualbroker\\Cache\\BalanceWorker.cs", "ViewState": "AgIAAEIAAAAAAAAAAAApwFAAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-23T13:37:47.603Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "ChartController.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Controllers\\Api\\v1\\ChartController.cs", "RelativeDocumentMoniker": "Services\\ot-webcore\\Controllers\\Api\\v1\\ChartController.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Controllers\\Api\\v1\\ChartController.cs", "RelativeToolTip": "Services\\ot-webcore\\Controllers\\Api\\v1\\ChartController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T12:39:51.466Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "demo.html", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\wwwroot\\otwebapi\\demo.html", "RelativeDocumentMoniker": "Services\\ot-webcore\\wwwroot\\otwebapi\\demo.html", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\wwwroot\\otwebapi\\demo.html", "RelativeToolTip": "Services\\ot-webcore\\wwwroot\\otwebapi\\demo.html", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001512|", "WhenOpened": "2025-05-14T12:32:37.797Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "Program.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Program.cs", "RelativeDocumentMoniker": "Services\\ot-webcore\\Program.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Program.cs", "RelativeToolTip": "Services\\ot-webcore\\Program.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAuwC0AAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T15:49:08.286Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\appsettings.Development.json", "RelativeDocumentMoniker": "Services\\ot-webcore\\appsettings.Development.json", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\appsettings.Development.json", "RelativeToolTip": "Services\\ot-webcore\\appsettings.Development.json", "ViewState": "AgIAABUAAAAAAAAAAAAAACQAAACEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-12T15:15:36.905Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "appsettings.pro.json", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\appsettings.pro.json", "RelativeDocumentMoniker": "Services\\ot-webcore\\appsettings.pro.json", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\appsettings.pro.json", "RelativeToolTip": "Services\\ot-webcore\\appsettings.pro.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-12T15:15:35.432Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "IServiceCollectionExtensions.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\brokerconnector\\Extensions\\IServiceCollectionExtensions.cs", "RelativeDocumentMoniker": "Services\\brokerconnector\\Extensions\\IServiceCollectionExtensions.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\brokerconnector\\Extensions\\IServiceCollectionExtensions.cs", "RelativeToolTip": "Services\\brokerconnector\\Extensions\\IServiceCollectionExtensions.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAqwB4AAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T15:11:54.084Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "class_microsoft_1_1_extensions_1_1_dependency_injection_1_1_i_service_collection_extensions-members.html", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\wwwroot\\docs\\df\\dec\\class_microsoft_1_1_extensions_1_1_dependency_injection_1_1_i_service_collection_extensions-members.html", "RelativeDocumentMoniker": "Services\\ot-webcore\\wwwroot\\docs\\df\\dec\\class_microsoft_1_1_extensions_1_1_dependency_injection_1_1_i_service_collection_extensions-members.html", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\wwwroot\\docs\\df\\dec\\class_microsoft_1_1_extensions_1_1_dependency_injection_1_1_i_service_collection_extensions-members.html", "RelativeToolTip": "Services\\ot-webcore\\wwwroot\\docs\\df\\dec\\class_microsoft_1_1_extensions_1_1_dependency_injection_1_1_i_service_collection_extensions-members.html", "ViewState": "AgIAAFQAAAAAAAAAAAApwGQAAADMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001512|", "WhenOpened": "2025-05-12T15:11:50.272Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "appsettings.json", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\appsettings.json", "RelativeDocumentMoniker": "Services\\ot-webcore\\appsettings.json", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\appsettings.json", "RelativeToolTip": "Services\\ot-webcore\\appsettings.json", "ViewState": "AgIAADMAAAAAAAAAAAAAAAMAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-12T15:10:03.07Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "Startup.cs", "DocumentMoniker": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Startup.cs", "RelativeDocumentMoniker": "Services\\ot-webcore\\Startup.cs", "ToolTip": "C:\\development\\OT\\OT-O2x\\Services\\ot-webcore\\Startup.cs", "RelativeToolTip": "Services\\ot-webcore\\Startup.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-12T14:03:03.73Z"}]}]}]}