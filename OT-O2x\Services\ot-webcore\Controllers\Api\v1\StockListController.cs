using MagicOnionHelper;
using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using OT.Common.Broker.Grpc.Connector;
using OT.Common.Broker.Grpc.V2;
using OT.Common.Broker.Models;
using OT.Common.Broker.Models.Info;
using OT.Common.Broker.Models.Permissions;
using OT.Common.Broker.Models.Portfolio.Parameters;
using OT.Common.Broker.Models.Properties.OLD;
using OT.Common.Cache.Grpc;
using OT.Common.Cache.Models;
using OT.Common.Cache.Models.Search;
using OT.Common.Customer.Grpc;
using OT.Common.Customer.Models.Account;
using OT.Common.Customer.Models.Views;
using OT.Common.Extensions;
using OT.Common.Models;
using OT.Common.Models.Market;
using OT.Common.Models.Stocks;
using OT.Common.Models.Stocks.Info.Prices;
using OT.Flowable.Flow;
using OT.Utils.Common.Helpers.TimeZoneHelper;
using OT.WebCore.Base;
using OT.WebCore.Controllers.api;
using OT.WebCore.Controllers.Api.v1.Base;
using OT.WebCore.Controllers.Base;
using OT.WebCore.Controllers.DTO;
using OT.WebCore.Entities;
using OT.WebCore.Models;
using OT.WebCore.Models.api.v1.Stock;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace OT.WebCore.Controllers.Api.v1
{
    [Authorize]
    public class StockListController : BaseApiController<StockListController>
    {
        private readonly IConfiguration _config;
        private readonly BrokerClient2<IBrokerServiceV2> _brokersService;

        public StockListController(
            BrokerClient2<IBrokerServiceV2> brokersService,
            IConfiguration configuration,
            TelemetryClient telemetryClient
            ) : base(configuration, telemetryClient)
        {
            _config = configuration;
            _brokersService = brokersService;

            _cacheProviderHost = _config.GetDatabaseConfiguration<string>("CACHE_PROVIDER_SERVICE_HOST");
            _cacheProviderPort = _config.GetDatabaseConfiguration<int>("CACHE_PROVIDER_SERVICE_PORT");
            _customerProviderHost = _config.GetDatabaseConfiguration<string>("CUSTOMER_PROVIDER_SERVICE_HOST");
            _customerProviderPort = _config.GetDatabaseConfiguration<int>("CUSTOMER_PROVIDER_SERVICE_PORT");
            _brokerConnectorHost = _config.GetDatabaseConfiguration<string>("BROKER_CONNECTOR_SERVICE_HOST");
            _brokerConnectorPort = _config.GetDatabaseConfiguration<int>("BROKER_CONNECTOR_SERVICE_PORT");

            if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development")
            {
                _cacheProviderPort = 6000;
                _customerProviderPort = 6005;
                _brokerConnectorPort = 6003;
            }
        }

        [HttpPost]
        [Route("GetStockList2")]
        public async Task<IActionResult> GetStockList2([FromBody] StockListModel stockListModel)
        {
            try
            {
                GetStockListResponse response = await GetStockListInternal(stockListModel, false);

                TrackTelemetry(_telemetryClient, "GetStockList2", new Dictionary<string, string>()
                {
                    { "StockListType", stockListModel.Type.ToString() },
                    { "StockListCode", stockListModel.Code },
                    { "Page", stockListModel.Paging.Page.ToString() },
                    { "PageSize", stockListModel.Paging.PageSize.ToString() },
                    { "Count", response.Data?.Count.ToString() ?? "null" }
                });

                return ApiResult(StatusCodes.Status200OK, response);
            }
            catch (Exception ex)
            {
                TrackTelemetry(_telemetryClient, ex, new Dictionary<string, string>()
                {
                    { "StockListType", stockListModel.Type.ToString() },
                    { "StockListCode", stockListModel.Code },
                });

                return ApiResult(StatusCodes.Status500InternalServerError, null);
            }
        }

        [HttpPost]
        [Route("GetStockList3")]
        public async Task<IActionResult> GetStockList3([FromBody] StockListModel stockListModel)
        {
            try
            {
                GetStockListResponse response = await GetStockListInternal(stockListModel, true);

                TrackTelemetry(_telemetryClient, "GetStockList3", new Dictionary<string, string>()
                {
                    { "StockListType", stockListModel.Type.ToString() },
                    { "StockListCode", stockListModel.Code },
                    { "Page", stockListModel.Paging.Page.ToString() },
                    { "PageSize", stockListModel.Paging.PageSize.ToString() },
                    { "Count", response.Data?.Count.ToString() ?? "null" }
                });

                return ApiResult(StatusCodes.Status200OK, response);
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, new Dictionary<string, string>()
                {
                    { "StockListType", stockListModel.Type.ToString() },
                    { "StockListCode", stockListModel.Code },
                });

                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        private async Task<GetStockListResponse> GetStockListInternal(StockListModel stockListModel, bool getLogos)
        {
            GetStockListResponse result = new GetStockListResponse();

            SpecificBrokerProperties brokerProperties;

            if (stockListModel.Broker == BrokerName.None)
            {
                Logger.LogWarning("Broker not valorised");
                brokerProperties = await GetBrokerPropertiesDefault();
            }
            else
            {
                brokerProperties = await GetBrokerPropertiesFull(stockListModel.Broker);
            }

            string title = string.Empty;
            List<FlowData> stockList = new();
            if (brokerProperties != null)
            {
                List<string> brokerCustomers = new List<string>();
                brokerProperties.Accounts.ForEach(account =>
                {
                    if (!string.IsNullOrEmpty(account.BrokerCustomer))
                        brokerCustomers.Add(account.BrokerCustomer);
                });

                List<StockKey> keys = null;
                List<string> columns = null;
                PersonalList pl = null;
                switch (stockListModel.Type)
                {
                    case StockListType.Basket:
                        StockBasketOperationResult res = await GetStocksByBasket(
                            stockListModel.Code,
                            stockListModel.ExtraFilters,
                            stockListModel.Filter,
                            //stockListModel.Paging,
                            PagingSelector.Default,  // paging in web
                            stockListModel.Sorting
                            );
                        keys = res.Stocks.Select(s => new StockKey(s.MarketCode, s.StockCode)).ToList();
                        columns = stockListModel.Cols;

                        result.Title = res.BasketDescription;
                        break;

                    case StockListType.PersonalList:
                        var personalList = await GetPersonaListData(stockListModel.Code, this.GetCustomerID(), stockListModel.Paging);
                        pl = personalList.Item1;

                        keys = personalList.Item1.GetStockKeys();
                        columns = stockListModel.Cols;

                        result.Title = personalList.Item1.Name;
                        break;

                    case StockListType.Preferiti:
                        keys = stockListModel.Stocks;
                        columns = stockListModel.Cols;

                        result.Title = "Preferiti";

                        break;

                    case StockListType.PortfolioList:
                        string accessToken = ControllerContext.GetTokenValue(stockListModel.Broker);
                        PortfolioResult portfolioResult = await _brokersService.GetClient(stockListModel.Broker).GetPortfolio(new PortfolioFilter() { AccessToken = accessToken, BrokerName = stockListModel.Broker });
                        keys = portfolioResult.Positions.Select(p => p.Stock).ToList();
                        columns = stockListModel.Cols;

                        result.Title = "Lista Titoli in Portafoglio";
                        break;

                    default:
                        break;
                }

                stockList = await GetFlowDataAsync(brokerCustomers, brokerProperties, keys, columns, pl);
            }

            if (stockListModel.Sorting.IsActive)
            {
                Func<IEnumerable<FlowData>, IOrderedEnumerable<FlowData>> sortingFunc = null;
                switch (stockListModel.Sorting.Direction)
                {
                    case Direction.Asc:
                        sortingFunc = p => p.OrderBy(x => x.Values.ContainsKey(stockListModel.Sorting.ColumnName) ? x.Values[stockListModel.Sorting.ColumnName] : null);
                        break;
                    case Direction.Desc:
                        sortingFunc = p => p.OrderByDescending(x => x.Values.ContainsKey(stockListModel.Sorting.ColumnName) ? x.Values[stockListModel.Sorting.ColumnName] : null);
                        break;
                }

                stockList = sortingFunc(stockList)
                    .ToList();
            }
            int totalStocks = stockList.Count;
            if (stockListModel.Paging.IsValorized)
            {
                stockList = stockList
                    .Skip(stockListModel.Paging.Page * stockListModel.Paging.PageSize)
                    .Take(stockListModel.Paging.PageSize)
                    .ToList();
            }

            result.Paging = new PagingResult(
                stockListModel.Paging,
                totalStocks
                );

            if (getLogos && stockList.Any())
            {
                var sList = new List<StockKey>();
                foreach (string[] split in stockList.Select(_ => _.Key.Split('.'))
                                                     .Where(_ => _.Length == 2)
                                                     .ToList())
                {
                    sList.Add(new StockKey(split[0], split[1]));
                }

                if (sList.Any())
                {
                    MagicOnionClient<IContentService> handle = new(false, _cacheProviderHost, _cacheProviderPort);
                    var res = await handle.Client.GetLogos(sList).ResponseAsync;
                    var basePath = Path.Combine(@"wwwroot", "Loghi");
                    foreach (FlowData fd in stockList)
                    {
                        var logo = res.FirstOrDefault(_ => $"{_.MarketCode}.{_.StockCode}" == fd.Key);
                        if (logo != null)
                        {
                            fd.Values["logo_dark"] = Path.Combine("loghi", logo.Dark);
                            fd.Values["logo_light"] = Path.Combine("loghi", logo.Light);
                        }
                    }
                }
            }

            result.Data = stockList;
            return result;
        }

        private async Task<List<FlowData>> GetFlowDataAsync(List<string> brokerCustomers, SpecificBrokerProperties brokerProperties, List<StockKey> keys, List<string> cols, PersonalList pl)
        {
            List<FlowData> stockList = new List<FlowData>();
            // Pull the data from the memory provider
            if (keys != null && keys.Any() &&
                cols != null && cols.Any())
            {
                MagicOnionClient<ICacheService> handleCache = new MagicOnionClient<ICacheService>(_cacheProviderHost, _cacheProviderPort);
                List<StockCacheResponse> stockValues =
                    await handleCache.Client.GetFullStockWithVisibilityCheck(brokerCustomers, keys).ResponseAsync;

                if (pl != null)
                    stockList = GetFlowDataNoFilter(stockValues, cols, brokerProperties, pl);
                // StockList PL già ordinata per ORDER
                else
                {
                    stockList = GetFlowDataNoFilter(stockValues, cols, brokerProperties);
                }
            }
            return stockList;
        }

        #region GetStockList
        private async Task<StockBasketOperationResult> GetStocksByBasket(string basketcode, string extrafilters,
            SearchFilter searchFilter, PagingSelector paging, WebFlowDataSortModel sorting)
        {
            MagicOnionClient<ICacheService> handle = new(false, _cacheProviderHost, _cacheProviderPort);
            return await handle.Client.GetStocksByBasket(basketcode, extrafilters, searchFilter, paging).ResponseAsync;
        }
        private async Task<(PersonalList, int)> GetPersonaListData(string code, int customerID, PagingSelector paging)
        {
            //int pListID = int.Parse(code);

            MagicOnionClient<IAccountService> handle = new MagicOnionClient<IAccountService>(_customerProviderHost, _customerProviderPort);
            List<PersonalList> personalLists =
                await handle.Client.GetPersonalListWithContent(customerID, code, null).ResponseAsync;

            PersonalList result = personalLists.FirstOrDefault();

            if (result == null)
                result = new PersonalList();

            int totalRows = result.Details.Count();
            return new(result, totalRows);
        }
        #endregion

        [HttpPost]
        //[AllowAnonymous]
        [Route("GetStockListByFilter2")]
        public async Task<IActionResult> GetStockListByFilter2([FromBody] StockListFilterModel model)
        {
            try
            {
                SpecificBrokerProperties brokerProperties = await GetBrokerPropertiesDefault();

                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                List<StockCacheResponse> stockValues = await magicOnionClient.Client.GetStockByFilter(model.Filter);

                List<FlowData> stockList = GetFlowData(stockValues, model.Cols, brokerProperties);
                stockList = AddLogos(stockList).GetAwaiter().GetResult();

                TrackTelemetry(_telemetryClient, "GetStockListByFilter2", new Dictionary<string, string>()
                {
                        { "Count", stockList?.Count.ToString() ?? "null" }
                });

                return ApiResult(StatusCodes.Status200OK, new { Data = stockList });
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, new Dictionary<string, string>()
                {
                });

                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        private async Task<List<FlowData>> AddLogos(List<FlowData> stockList)
        {
            List<StockKey> sList = new List<StockKey>();
            stockList.ForEach(item =>
            {
                string[] tokens = item.Key.Split('.', 2);
                string MarketCode = tokens[0];
                string StockCode = tokens[1];
                sList.Add(new StockKey(MarketCode, StockCode));
            });

            MagicOnionClient<IContentService> handle = new(false, _cacheProviderHost, _cacheProviderPort);

            if (sList.Any())
            {
                var logos = await handle.Client.GetLogos(sList).ResponseAsync;
                var basePath = Path.Combine(@"wwwroot", "Loghi");
                if (logos != null && logos.Any())
                {
                    foreach (var s in stockList)
                    {
                        string[] tokens = s.Key.Split('.', 2);
                        string marketCode = tokens[0];
                        string stockCode = tokens[1];

                        var logo = logos.FirstOrDefault(_ => _.MarketCode == marketCode && _.StockCode == stockCode);
                        if (logo != null)
                        {
                            s.Values["logo_dark"] = Path.Combine("loghi", logo.Dark);
                            s.Values["logo_light"] = Path.Combine("loghi", logo.Light);
                        }
                    }
                }
            }
            return stockList;
        }

        [HttpPost]
        [Route("GetVolatilityChartData")]
        public async Task<IActionResult> GetVolatilityChartData([FromBody] VolatilityChartDataModel model)
        {
            try
            {
                StockKey underlying = new StockKey(model.UnderlyingMarketCode, model.UnderlyingStockCode);

                // Retrieve the list of related options
                MagicOnionClient<ICacheService> handle = new MagicOnionClient<ICacheService>(_cacheProviderHost, _cacheProviderPort);
                List<Stock> options = await handle.Client.GetOptionsByUnderlying(underlying, model.ExpiryDate).ResponseAsync;

                HashSet<string> columns = new() { "VOLATILITY", "BID_PRICE_1", "ASK_PRICE_1" };

                List<StockKey> keys = options.Select(s => new StockKey(s.MarketCode, s.StockCode)).ToList();
                // TODO - Era tutto finto, sono da rifare visto che il GetData fa return null alla fine
                List<FlowData> optionsData = new(); //PullServiceProxy.Instance.PullData(keys, columns);

                //  TODO - ripristinare
                //// Complete data with the DB information
                //foreach (PushData pushData in optionsData)
                //{
                //    Stock stock = StockManager.GetStock(pushData.Stock);
                //    if (stock != null)
                //    {
                //        if (!pushData.Values.Any(pv => pv.Column == "STRIKE_PRICE"))
                //            pushData.Values.Add(new PushValue("STRIKE_PRICE", stock.StrikePrice));
                //        if (!pushData.Values.Any(pv => pv.Column == "CALL_PUT"))
                //            pushData.Values.Add(new PushValue("CALL_PUT", stock.CallPut));
                //    }
                //}
                TrackTelemetry(_telemetryClient, "GetVolatilityChartData", new Dictionary<string, string>()
                {

                });

                if (optionsData != null && optionsData.Any())
                    return ApiResult(StatusCodes.Status200OK, optionsData);
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, new Dictionary<string, string>()
                {

                });
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpPost]
        //[AllowAnonymous]
        [Route("GetStock")]
        public async Task<IActionResult> GetStock([FromBody] StockModel stockModel)
        {
            Dictionary<string, string> telemetryData = stockModel.GetTelemetryData();

            try
            {
                var result = await GetStockInternal(stockModel);

                TrackTelemetry(_telemetryClient, "GetStock", telemetryData);

                return result;
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, telemetryData);
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }
        [HttpPost]
        [Route("GetStock2")]
        public async Task<IActionResult> GetStock2([FromBody] StockModel stockModel)
        {
            Dictionary<string, string> telemetryData = stockModel.GetTelemetryData();

            try
            {
                var result = await GetStockInternal(stockModel, true);
                TrackTelemetry(_telemetryClient, "GetStock2", telemetryData);

                return result;
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, telemetryData);
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpPost]
        [Route("GetStockInternal")]
        private async Task<IActionResult> GetStockInternal([FromBody] StockModel stockModel, bool getLogos = false)
        {
            // Enable CORS for this web methods - TODO - da ripristinare
            Response.Headers.Append("Access-Control-Allow-Origin", "*");
            MagicOnionClient<ICacheService> handle = new(_cacheProviderHost, _cacheProviderPort);

            var allProperties = await GetBrokerPropertiesFull();
            SpecificBrokerProperties brokerProperties = allProperties.FirstOrDefault().Value;

            List<string> brokerCustomers = new List<string>();
            brokerProperties.Accounts.ForEach(account =>
            {
                if (!string.IsNullOrEmpty(account.BrokerCustomer))
                    brokerCustomers.Add(account.BrokerCustomer);
            });

            List<StockCacheResponse> lr = await handle.Client
                .GetFullStockWithVisibilityCheck(
                    brokerCustomers,
                    new List<StockKey>() { new StockKey(stockModel.MarketCode, stockModel.StockCode) },
                    true)
                .ResponseAsync;

            StockCacheResponse r = lr.FirstOrDefault();

            if (r != null)
            {
                if (getLogos)
                {
                    var sList = new List<StockKey>();
                    sList.Add(new StockKey()
                    {
                        MarketCode = r.MarketCode,
                        StockCode = r.StockCode
                    });
                    MagicOnionClient<IContentService> handleContent = new(false, _cacheProviderHost, _cacheProviderPort);
                    var logos = await handleContent.Client.GetLogos(sList).ResponseAsync;
                    var r_flowData = r.GetFlowData();
                    if (logos != null && logos.Any())
                    {
                        var logo = logos.FirstOrDefault(_ => _.StockCode == r.StockCode &&
                                                             _.MarketCode == r.MarketCode);
                        if (logo != null)
                        {
                            //FileInfo logoDark = new(Path.Combine(basePath, logo.Dark));
                            r.LogoDark = Path.Combine("loghi", logo.Dark);
                            //FileInfo LogoLight = new(Path.Combine(basePath, logo.Light));
                            r.LogoLight = Path.Combine("loghi", logo.Light);
                        }
                    }

                }
                return ApiResult(StatusCodes.Status200OK, new { Data = r });
            }

            return ApiResult(StatusCodes.Status204NoContent, null);
        }

        [HttpGet("GetStockUserInfo")]
        public async Task<IActionResult> GetStockUserInfo(string marketCode, string stockCode)
        {
            try
            {
                var result = await GetStockUserInfoInternal(marketCode, stockCode);
                TrackTelemetry(_telemetryClient, "GetStockUserInfo", new Dictionary<string, string>()
                {
                    { "MarketCode", marketCode },
                    { "StockCode", stockCode }
                });
                return ApiResult(StatusCodes.Status200OK, result);
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, new Dictionary<string, string>()
                {
                    { "MarketCode", marketCode },
                    { "StockCode", stockCode }
                });
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpPost("GetStockUserInfo")]
        public async Task<IActionResult> GetStockUserInfo([FromBody] StockModel model)
        {
            Dictionary<string, string> telemetryData = model.GetTelemetryData();

            try
            {
                var result = await GetStockUserInfoInternal(model.MarketCode, model.StockCode);
                TrackTelemetry(_telemetryClient, "GetStockUserInfo", telemetryData);
                return ApiResult(StatusCodes.Status200OK, result);
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, telemetryData);
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        private async Task<object> GetStockUserInfoInternal(string marketCode, string stockCode)
        {
            List<object> result = new List<object>();

            MagicOnionClient<ICacheService> handle = new(_cacheProviderHost, _cacheProviderPort);

            string accessToken = ControllerContext.GetTokenValue(BrokerName.Sella);
            string userId = this.GetUserId();
            if (string.IsNullOrEmpty(userId))
            {
                var prop = await _brokersService.GetClient(BrokerName.Sella).GetUserState(BrokerName.Sella, this.GetCustomerID()).ResponseAsync;
            }

            StockKey sk = new StockKey(marketCode, stockCode);
            List<StockKey> list = new() { sk };
            List<StockCacheResponse> lr = await handle.Client.GetFullStock(list, true).ResponseAsync;
            StockCacheResponse r = lr.FirstOrDefault(k => k.Stock == sk);

            //if(r.Stock.ShortLeverageInfo)  // TODO: checks
            var userTradeInfo = await _brokersService.GetClient(BrokerName.Sella).GetUserTradeInfo(BrokerName.Sella, accessToken, r.Stock.TradeGroupId);
            var info = await _brokersService.GetClient(BrokerName.Sella).GetFillOrderMask(new FillOrderMaskParameters()
            {
                StockKey = new(marketCode, stockCode),
                AccessToken = accessToken,
                Broker = BrokerName.Sella,
                CustomerId = this.GetCustomerID(),
                ClientInfo = this.GetClientInfo()
            });

            var phase = OrderPhase.AperturaContinuaChiusura;  // default?
            if (int.TryParse(info.MarketPhase, out int p))
            {
                phase = (OrderPhase)p;
            }

            if (phase == OrderPhase.BuioMattina
                || phase == OrderPhase.Apertura
                || phase == OrderPhase.BuioApertura
                || phase == OrderPhase.FineServizio)
            {
                phase = OrderPhase.AperturaContinuaChiusura;
            }

            return new
            {
                Stock = r,
                UserInfo = userTradeInfo,
                info.ExecutionInfo,
                info.ValidDays,
                info.ValidDaysBatch,
                OrderPhase = phase.ToString(),
                info.HolderProductInfo,
                info.PositionLimit
            };
        }


        [HttpPost]
        //[AllowAnonymous]
        [Route("GetStocks")]
        public async Task<IActionResult> GetStocks([FromBody] StocksModel stocksModel)
        {
            Dictionary<string, string> telemetryData = stocksModel.GetTelemetryData();

            try
            {
                // Enable CORS for this web methods - TODO - da ripristinare
                Response.Headers.Append("Access-Control-Allow-Origin", "*");
                SpecificBrokerProperties brokerProperties = await GetBrokerPropertiesDefault();

                MagicOnionClient<ICacheService> handle = new(_cacheProviderHost, _cacheProviderPort);

                List<StockCacheResponse> lr = await handle.Client.GetFullStock(stocksModel.Stocks).ResponseAsync;
                List<FlowData> stockList = GetFlowData(lr, stocksModel.Cols, brokerProperties);

                telemetryData.Add("Count", stockList?.Count.ToString() ?? "null");
                TrackTelemetry(_telemetryClient, "GetStocks", telemetryData);

                if (stockList != null && stockList.Any())
                    return ApiResult(StatusCodes.Status200OK, new { Data = stockList });
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, telemetryData);
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("GetStockForDetail2")]
        public async Task<IActionResult> GetStockForDetail2([FromBody] StocksModel stocksModel)
        {
            Dictionary<string, string> telemetryData = stocksModel.GetTelemetryData();

            try
            {
                Response.Headers.Append("Access-Control-Allow-Origin", "*");
                SpecificBrokerProperties brokerProperties = await GetBrokerPropertiesDefault();
                List<string> brokerCustomer = new List<string>() { brokerProperties.Accounts.FirstOrDefault().BrokerCustomer };

                MagicOnionClient<ICacheService> handle = new(_cacheProviderHost, _cacheProviderPort);

                List<StockCacheResponse> lr = await handle.Client.GetFullStockWithVisibilityCheck(brokerCustomer, stocksModel.Stocks).ResponseAsync;
                List<FlowData> stockList = GetFlowData(lr, stocksModel.Cols, brokerProperties);
                StockCacheResponse lrElement = lr.FirstOrDefault();
                FlowData stockElement = stockList.FirstOrDefault();

                StockType stockType = await handle.Client.GetStockType(lrElement.Stock.MarketCode, lrElement.Stock.StockType).ResponseAsync;
                StockSubType stockSubType = await handle.Client.GetStockSubType(lrElement.Stock.MarketCode, lrElement.Stock.StockSubType).ResponseAsync;
                if (stockType != null)
                    stockElement.Values.UpdateMerge(stockType.GetFlowData(stocksModel.Cols).Values);
                if (stockSubType != null)
                    stockElement.Values.UpdateMerge(stockSubType.GetFlowData(stocksModel.Cols).Values);

                telemetryData.Add("Count", stockList?.Count.ToString() ?? "null");
                TrackTelemetry(_telemetryClient, "GetStockForDetail2", telemetryData);

                if (stockList != null && stockList.Any())
                    return ApiResult(StatusCodes.Status200OK, new { Data = stockList });
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, telemetryData);
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpPost]
        //[AllowAnonymous]
        [Route("GetStockRisk")]
        public async Task<IActionResult> GetStockRisk([FromBody] StockModel stockModel)
        {
            Dictionary<string, string> telemetryData = stockModel.GetTelemetryData();

            try
            {
                Response.Headers.Append("Access-Control-Allow-Origin", "*");
                MagicOnionClient<ICacheService> handle = new(_cacheProviderHost, _cacheProviderPort);

                StockKey sk = new(stockModel.MarketCode, stockModel.StockCode);
                List<StockKey> list = new() { sk };
                List<StockCacheResponse> lr = await handle.Client.GetFullStock(list).ResponseAsync;
                StockCacheResponse r = lr.FirstOrDefault(k => k.Stock == sk);

                TrackTelemetry(_telemetryClient, "GetStockRisk", telemetryData);

                if (r != null)
                    return ApiResult(StatusCodes.Status200OK, new { Data = r.Stock/*.RiskDet*/ });
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, telemetryData);
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpPost]
        //[AllowAnonymous]
        [Route("GetStockForDetail")]
        public async Task<IActionResult> GetStockForDetail([FromBody] StockModel stockModel)
        {
            Dictionary<string, string> telemetryData = stockModel.GetTelemetryData();

            try
            {
                // Enable CORS for this web methods
                Response.Headers.Append("Access-Control-Allow-Origin", "*");
                MagicOnionClient<ICacheService> handle = new(_cacheProviderHost, _cacheProviderPort);

                StockKey sk = new(stockModel.MarketCode, stockModel.StockCode);
                List<StockKey> list = new() { sk };
                List<StockCacheResponse> lr = await handle.Client.GetFullStock(list).ResponseAsync;
                StockCacheResponse r = lr.FirstOrDefault(k => k.Stock == sk);

                List<object> returnList = new()
                {
                    r.Stock,
                    r.Price,
                    r.Book,
                    r.Event
                };

                telemetryData.Add("Count", returnList?.Count.ToString() ?? "null");
                TrackTelemetry(_telemetryClient, "GetStockForDetail", telemetryData);

                if (returnList.Any())
                    return ApiResult(StatusCodes.Status200OK, new { Data = returnList });
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, telemetryData);
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpPost]
        [Route("GetMarkets")]
        public async Task<IActionResult> GetMarkets()
        {
            try
            {
                MagicOnionClient<ICacheService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                List<Market> markets = await magicOnionClient.Client.GetMarkets().ResponseAsync;

                TrackTelemetry(_telemetryClient, "GetMarkets", new Dictionary<string, string>()
                {
                    { "Count" , markets?.Count.ToString() ?? "null" }
                });

                if (markets != null && markets.Any())
                    return ApiResult(StatusCodes.Status200OK, new { Data = markets });
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, new Dictionary<string, string>()
                {
                });
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpPost]
        [Route("GetBaskets")]
        public async Task<IActionResult> GetBaskets()
        {
            try
            {
                //MagicOnionClient<ICacheService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                //List<Market> markets = await magicOnionClient.Client.GetMarkets().ResponseAsync;

                string basketList = _config.GetValue("BASKET_LIST", ".");
                List<Basket> baskets = basketList.Split(',').Select(x => new Basket(x.Split('|')[0], x.Split('|')[1])).ToList();

                TrackTelemetry(_telemetryClient, "GetBaskets", new Dictionary<string, string>()
                {
                    { "Count" , baskets?.Count.ToString() ?? "null" }
                });

                if (baskets != null && baskets.Any())
                    return ApiResult(StatusCodes.Status200OK, new { Data = baskets });
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, new Dictionary<string, string>()
                {
                });
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpGet]
        [Route("GetMarketInformation")]
        public async Task<IActionResult> GetMarketInformation()
        {
            try
            {
                MagicOnionClient<ICacheService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                string markets = await magicOnionClient.Client.GetMarketInformation().ResponseAsync;

                TrackTelemetry(_telemetryClient, "GetMarketInformation", new Dictionary<string, string>()
                {
                });

                if (markets != null && markets.Any())
                    return ApiResult(StatusCodes.Status200OK, new { Data = markets });
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, new Dictionary<string, string>()
                {
                });
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpPost]
        //[AllowAnonymous]
        [Route("GetMarketGroups")]
        public async Task<IActionResult> GetMarketGroups()
        {
            try
            {
                MagicOnionClient<ICacheService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                List<string> marketGroups = await magicOnionClient.Client.GetMarketGroups().ResponseAsync;

                TrackTelemetry(_telemetryClient, "GetMarketGroups", new Dictionary<string, string>()
                {
                    { "Count" , marketGroups?.Count.ToString() ?? "null" }
                });

                if (marketGroups != null && marketGroups.Any())
                    return ApiResult(StatusCodes.Status200OK, new { Data = marketGroups });
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                TrackTelemetry(_telemetryClient, exc, new Dictionary<string, string>() { });
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpPost]
        //[AllowAnonymous]
        [Route("GetFastSearchInstruments")]
        public async Task<IActionResult> GetFastSearchInstruments([FromBody] object stockCode)
        {
            List<Stock> st = new();
            try
            {
                IDictionary<string, string> values = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, string>>(stockCode.ToString());

                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                List<StockCacheResponse> stockValues = await magicOnionClient.Client.GetStockByFastSearch(values["stockCode"]);

                foreach (StockCacheResponse item in stockValues)
                    st.Add(item.Stock);

                if (st.Any())
                    return ApiResult(StatusCodes.Status200OK, new { Data = st });
                return ApiResult(StatusCodes.Status204NoContent, null);

            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "GetFastSearchInstruments:");
                return ApiResult(StatusCodes.Status500InternalServerError, ex.Message);
            }
        }

        [HttpPost("GetFastSearchInstrumentsGrid")]  // TODO: vedere se usata
        public async Task<IActionResult> GetFastSearchInstrumentsGrid([FromBody] object stockCode) // object stockCode, int pageSize = 10, int page = 0)
        {
            IDictionary<string, string> values = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, string>>(stockCode.ToString());
            FlowDataResult result = await GetFastSearchInstrumentsGridInternal("", values["stockCode"], new List<string>(), -1);
            if (result.Result.Any())
                return ApiResult(StatusCodes.Status200OK, new { Data = result.Result });
            return ApiResult(StatusCodes.Status204NoContent, null);
        }

        [HttpPost("GetFastSearchInstrumentsGridPaged")]
        public async Task<IActionResult> GetFastSearchInstrumentsGridPaged([FromBody] StockSearchModel stockSearch)
        {
            FlowDataResult result = await GetFastSearchInstrumentsGridInternal("", stockSearch.search, stockSearch.Cols, stockSearch.Paging.PageSize, stockSearch.Paging.Page);
            if (result.Result.Any())
                return ApiResult(StatusCodes.Status200OK, new { Data = result });
            return ApiResult(StatusCodes.Status204NoContent, null);
        }

        [HttpPost("GetFastSearchPagedWeb")]
        public async Task<IActionResult> GetFastSearchPagedWeb([FromBody] StockSearchModel stockSearch)
        {
            FlowDataResult result = await GetFastSearchInstrumentsGridInternal("", stockSearch.search, stockSearch.Cols, stockSearch.Paging.PageSize, stockSearch.Paging.Page);
            if (result.Result.Any())
            {
                var response = new
                {
                    Data = result.Result,
                    Paging = result.Paging
                };
                return ApiResult(StatusCodes.Status200OK, response);
            }
            return ApiResult(StatusCodes.Status204NoContent, null);
        }

        private async Task<FlowDataResult> GetFastSearchInstrumentsGridInternal(string marketCode, string stockCode, List<string> cols, int pageSize = 10, int page = 0)
        {
            FlowDataResult result = new FlowDataResult();
            result.Paging = new PagingResult();

            try
            {
                //int stockToGet = -1;
                //if (pageSize == -1)
                //{
                //    stockToGet = pageSize;
                //}

                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                StockCachePagedResponse searchRes = await magicOnionClient.Client.GetStockByFastSearchPaged(stockCode, null, null, pageSize, page, Criteria.BeginWith);

                List<StockCacheResponse> stockValues = searchRes.Items.Where(i => i.Stock.Key != ".").ToList();  // TMP?: fix per eventuali vuoti

                SpecificBrokerProperties brokerProperties = null;

                string title = string.Empty;
                var allProperties = await GetBrokerPropertiesFull();

                if (allProperties.ContainsKey(BrokerName.Sella))
                {
                    brokerProperties = allProperties[BrokerName.Sella];
                }
                else
                {
                    brokerProperties = allProperties.FirstOrDefault().Value;
                }

                if (pageSize != -1)
                {
                    result.Paging = searchRes.Paging;
                    //result.Paging.PageSize = pageSize;
                    //result.Paging.Page = page;
                    //stockValues = stockValues.Skip(pageSize * page).Take(pageSize).ToList();
                }

                List<FlowData> records = new List<FlowData>();
                foreach (StockCacheResponse item in stockValues)
                {
                    InfoAbil infoAbil = InfoAbil.None;

                    if (item.Stock?.InfoGroupId != null &&
                       (brokerProperties?.InfoPermissions.TryGetValue(item.Stock.InfoGroupId, out var perm) ?? false))
                    {
                        infoAbil = perm.InfoAbil;
                    }

                    //var allCols = FlowPivot<Stock>.GetDictionary(item.Stock, false).Keys.ToList();
                    //allCols.AddRange(FlowPivot<Price>.GetDictionary(item.Price, false).Keys.ToList());
                    //allCols.AddRange(FlowPivot<CalculatedPrice>.GetDictionary(item.CalculatedPrice, false).Keys.ToList());

                    bool hasFullInfo = (infoAbil & (InfoAbil.RealTime | InfoAbil.Push | InfoAbil.Graph | InfoAbil.Alarm)) != InfoAbil.None;
                    records.Add(item.GetFlowData(cols, brokerProperties?.IsTradable(item.Stock.TradeGroupId) ?? false, false, false, (short)infoAbil, hasFullInfo));
                }

                result.Result = records;
                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "GetFastSearchInstruments:");
                return null;
            }
        }

        [HttpGet]
        [Route("GetDerContextFilteredData")]
        public async Task<IActionResult> GetDerContextFilteredData(string market, string subType, string underlying)
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                List<DerContext> derContextData = await magicOnionClient.Client.GetDerContextData().ResponseAsync;
                if (derContextData != null && derContextData.Any())
                {
                    IEnumerable<DerContext> filteredData = derContextData;
                    if (!string.IsNullOrEmpty(market))
                    {
                        filteredData = filteredData.Where(i => i.Market_code == market);
                    }
                    if (!string.IsNullOrEmpty(subType))
                    {
                        string subTypeKey = derContextData.FirstOrDefault().GetSubTypeMapping().FirstOrDefault(x => x.Value == subType).Key;
                        filteredData = filteredData.Where(i => i.Stock_sub_type == subTypeKey);
                    }
                    if (!string.IsNullOrEmpty(underlying))
                    {
                        filteredData = filteredData.Where(i => i.Underlying_description == underlying);
                    }

                    var groupedData = GroupDerContextData(filteredData);

                    return ApiResult(StatusCodes.Status200OK, new { Data = groupedData });
                }
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        #region Get<X>SellaTraderContextDataSeparator DA DISMETTERE TRA QUALCHE VERSIONE DI APP a favore delle Get<X>ContextDataTree
        [HttpGet]
        [Route("GetDerSellaTraderContextDataSeparator")]
        public async Task<IActionResult> GetDerSellaTraderContextDataSeparator()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                Dictionary<string, List<Dictionary<string, string>>> derContextData =
                        await magicOnionClient.Client.GetContextSellaTraderData("Der-Combos").ResponseAsync;

                return ApiResult(StatusCodes.Status200OK, derContextData);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpGet]
        [Route("GetObbSellaTraderContextData")]
        public async Task<IActionResult> GetObbContextSellaTraderDataPipe()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                Dictionary<string, List<Dictionary<string, string>>> derContextData =
                    await magicOnionClient.Client.GetContextSellaTraderData("Obb-Combos-Pipe").ResponseAsync;


                return ApiResult(StatusCodes.Status200OK, derContextData);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpGet]
        [Route("GetObbSellaTraderContextDataSeparator")]
        public async Task<IActionResult> GetObbSellaTraderContextDataSeparator()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                Dictionary<string, List<Dictionary<string, string>>> derContextData =
                    await magicOnionClient.Client.GetContextSellaTraderData("Obb-Combos").ResponseAsync;

                return ApiResult(StatusCodes.Status200OK, derContextData);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpGet]
        [Route("GetCertSellaTraderContextDataSeparator")]
        public async Task<IActionResult> GetCertSellaTraderContextData()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                Dictionary<string, List<Dictionary<string, string>>> certContextData =
                    await magicOnionClient.Client.GetContextSellaTraderData("Cert-Combos").ResponseAsync;

                return ApiResult(StatusCodes.Status200OK, certContextData);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }
        #endregion

        #region Get<X>ContextData DA DISMETTERE una volta adeguato FE Web con Get<X>ContextDataTree
        [HttpGet]
        [Route("GetDerContextData")]
        public async Task<IActionResult> GetDerContextData()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                List<DerContext> derContextData = await magicOnionClient.Client.GetDerContextData().ResponseAsync;
                if (derContextData != null && derContextData.Any())
                {
                    var groupedData = GroupDerContextData(derContextData);

                    return ApiResult(StatusCodes.Status200OK, groupedData);
                }
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }
        private Dictionary<string, IEnumerable<object>> GroupDerContextData(IEnumerable<DerContext> derContextData)
        {
            return new Dictionary<string, IEnumerable<object>>()
            {
                { "market_codes", derContextData.GroupBy(i => i.Market_code).Select(i => i.Key) },
                { "stock_sub_types", derContextData.GroupBy(i => i.Stock_sub_type).Select(
                    i => new { key = i.Key, description = i.GroupBy(j => j.Stock_subtype_description).Select(j => j.Key) }) },
                { "underlying_descriptions", derContextData.GroupBy(i => i.Underlying_description).Select(i => i.Key).Where(i => !string.IsNullOrEmpty(i)) },
                { "multipliers", derContextData.GroupBy(i => i.ContractSize).Select(i => i.Key.ToString()) },
                { "call_puts", derContextData.GroupBy(i => i.Call_put).Select(i => i.Key) },
                { "years", derContextData.GroupBy(i => i.year).Select(i => i.Key) },
                { "months", derContextData.GroupBy(i => i.month).Select(i => i.Key) },
                { "days", derContextData.GroupBy(i => i.day).Select(i => i.Key) },
            };
        }

        [HttpGet("GetObbContextData")]
        public async Task<IActionResult> GetObbContextData(bool grouped)
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                List<ObbContext> obbContextData = await magicOnionClient.Client.GetObbContextData().ResponseAsync;

                if (obbContextData != null && obbContextData.Any())
                {
                    Dictionary<string, object> groupedData = GroupObbContextDataForXtrading(obbContextData);
                    return ApiResult(StatusCodes.Status200OK, new { Data = groupedData });
                }
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        private Dictionary<string, object> GroupObbContextDataForXtrading(IEnumerable<ObbContext> obbContextData)
        {
            return new Dictionary<string, object>()
            {
                { "market_codes", obbContextData.GroupBy(i => i.Market_code).Select(
                    i =>
                    new {
                        description = i.Key,
                        sub_types = i.GroupBy(j => j.Stock_sub_type).Select(j => j.Key),
                        stock_types = i.GroupBy(j => j.Stock_type).Select(j => j.Key),
                        currency = i.GroupBy(j => j.Currency).Select(j => j.Key),
                        expiry_date = i.GroupBy(j => j.ExpiryDate).Select(j => j.Key)
                    }) },
                { "stock_types", obbContextData.GroupBy(i => i.Stock_type).Select(
                    i => new { key = i.Key, description = i.GroupBy(j => j.Stock_type_description).Select(j => j.Key) }) },
                { "stock_sub_types", obbContextData.GroupBy(i => i.Stock_sub_type).Select(
                    i => new { key = i.Key, description = i.GroupBy(j => j.Stock_subtype_description).Select(j => j.Key) }) },
                { "currency", obbContextData.GroupBy(i => i.Currency).Select(i => i.Key) },
                {"expiry_date_from", obbContextData.GroupBy(i=>i.ExpiryDate).OrderBy(i=>i.Key).Select(
                    i=>i.Key ).FirstOrDefault() },
                {"expiry_date_to", obbContextData.GroupBy(i=>i.ExpiryDate).OrderByDescending(i=>i.Key).Select(
                     i=>i.Key ).FirstOrDefault()}
            };
        }

        [HttpGet]
        [Route("GetObbContextFilteredData")]
        public async Task<IActionResult> GetObbContextFilteredData(string market, string stockTypeString, string currency)
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                List<ObbContext> obbContextData = await magicOnionClient.Client.GetObbContextData().ResponseAsync;
                if (obbContextData != null && obbContextData.Any())
                {
                    IEnumerable<ObbContext> filteredData = obbContextData;
                    if (!string.IsNullOrEmpty(market) && market != "Mercato")
                    {
                        filteredData = filteredData.Where(i => i.Market_code == market);
                    }
                    if (!string.IsNullOrEmpty(stockTypeString))
                    {
                        string[] stockArray = stockTypeString.Split('-');
                        if (stockArray.Length > 0)
                        {
                            string stockType = stockArray[0].Trim();
                            string stockSubType = string.Empty;
                            if (stockArray.Length > 1)
                                stockSubType = stockArray[1].Trim();
                            if (!string.IsNullOrEmpty(stockType) && !string.IsNullOrEmpty(stockSubType))
                                filteredData = filteredData.Where(i => i.Stock_type == stockType && i.Stock_sub_type == stockSubType);
                            else if (!string.IsNullOrEmpty(stockType))
                                filteredData = filteredData.Where(i => i.Stock_type == stockType);
                            else if (!string.IsNullOrEmpty(stockSubType))
                                filteredData = filteredData.Where(i => i.Stock_sub_type == stockSubType);
                        }
                    }
                    if (!string.IsNullOrEmpty(currency))
                    {
                        filteredData = filteredData.Where(i => i.Currency == currency);
                    }

                    Dictionary<string, object> groupedData = GroupObbContextData(filteredData);

                    if (groupedData != null && groupedData.Any())
                        return ApiResult(StatusCodes.Status200OK, new { Data = groupedData });
                    else
                        return ApiResult(StatusCodes.Status204NoContent, null);
                }
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        private Dictionary<string, object> GroupObbContextData(IEnumerable<ObbContext> obbContextData)
        {
            return new Dictionary<string, object>()
            {
                { "market_codes", obbContextData.GroupBy(i => i.Market_code).Select(
                    i => new { description = i.Key, sub_types = i.GroupBy(j => j.Stock_sub_type).Select(j => j.Key) }) },
                { "stock_sub_types", obbContextData.GroupBy(i => i.Stock_sub_type).Select(
                    i => new { key = i.Key, description = i.GroupBy(j => j.Stock_subtype_description).Select(j => j.Key) }) },
                { "currency", obbContextData.GroupBy(i => i.Currency).Select(i => i.Key) },
                {"expiry_date_from", obbContextData.GroupBy(i=>i.ExpiryDate).OrderBy(i=>i.Key).Select(
                    i=>i.Key ).FirstOrDefault() },
                {"expiry_date_to", obbContextData.GroupBy(i=>i.ExpiryDate).OrderByDescending(i=>i.Key).Select(
                     i=>i.Key ).FirstOrDefault()}
            };
        }

        [HttpGet]
        [Route("GetCwContextData")]
        public async Task<IActionResult> GetCwContextData()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                List<CwContext> cwContextData = await magicOnionClient.Client.GetCwContextData().ResponseAsync;

                if (cwContextData != null && cwContextData.Any())
                    return ApiResult(StatusCodes.Status200OK, new { Data = cwContextData });
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpGet]
        [Route("GetCwContextFilteredData")]
        public async Task<IActionResult> GetCwContextFilteredData(string issuer, string subType, string underlying)
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                List<CwContext> cwContextData = await magicOnionClient.Client.GetCwContextData().ResponseAsync;
                if (cwContextData != null && cwContextData.Any())
                {
                    IEnumerable<CwContext> filteredData = cwContextData;
                    if (!string.IsNullOrEmpty(issuer) && issuer != "Mercato")
                    {
                        filteredData = filteredData.Where(i => i.Issuer == issuer);
                    }
                    if (!string.IsNullOrEmpty(subType))
                    {
                        filteredData = filteredData.Where(i => i.StockSubtype == subType);
                    }
                    if (!string.IsNullOrEmpty(underlying))
                    {
                        filteredData = filteredData.Where(i => i.Underlying_description == underlying);
                    }

                    Dictionary<string, object> groupedData = GroupCwContextData(filteredData);

                    return ApiResult(StatusCodes.Status200OK, new { Data = groupedData });
                }
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        private Dictionary<string, object> GroupCwContextData(IEnumerable<CwContext> cwContextData)
        {
            return new Dictionary<string, object>()
            {
                { "issuers", cwContextData.GroupBy(i => i.Issuer).Select(i => i.Key) },
                { "stock", cwContextData.GroupBy(i => i.StockSubtype).Select(i => i.Key.ToString()) },
                { "stock_sub_types", cwContextData.GroupBy(i => i.Stock_subtype_description).Select(i => i.Key) },
                { "underlying_descriptions", cwContextData.GroupBy(i => i.Underlying_description).Select(i => i.Key).Where(i => !string.IsNullOrEmpty(i)) },
                { "years", cwContextData.GroupBy(i => i.Year).Select(i => i.Key) },
                { "months", cwContextData.GroupBy(i => i.Month).Select(i => i.Key) },
            };
        }
        #endregion

        #region Get<X>ContextDataTree(Mobile) versione finale
        [HttpGet]
        [Route("GetCertContextDataTree")]
        public async Task<IActionResult> GetCertContextDataTree()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                Dictionary<string, List<Dictionary<string, string>>> certContextData =
                    await magicOnionClient.Client.GetContextSellaTraderData("Cert-Combos-V1").ResponseAsync;

                return ApiResult(StatusCodes.Status200OK, certContextData);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }
        [HttpGet]
        [Route("GetCertContextDataTreeMobile")]
        public async Task<IActionResult> GetCertContextDataTreeMobile()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                Dictionary<string, List<Dictionary<string, string>>> certContextData =
                    await magicOnionClient.Client.GetContextSellaTraderData("Cert-Combos-V1").ResponseAsync;

                return ApiResult(StatusCodes.Status200OK, certContextData);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }
        [HttpGet]
        [Route("GetDerContextDataTree")]
        public async Task<IActionResult> GetDerContextDataTree()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                Dictionary<string, List<Dictionary<string, string>>> derContextData =
                        await magicOnionClient.Client.GetContextSellaTraderData("Der-Combos-V1").ResponseAsync;

                return ApiResult(StatusCodes.Status200OK, derContextData);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }
        [HttpGet]
        [Route("GetDerContextDataTreeMobile")]
        public async Task<IActionResult> GetDerContextDataTreeMobile()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                Dictionary<string, List<Dictionary<string, string>>> derContextData =
                        await magicOnionClient.Client.GetContextSellaTraderData("Der-Combos-Mobile-V1").ResponseAsync;

                return ApiResult(StatusCodes.Status200OK, derContextData);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpGet]
        [Route("GetObbContextDataTree")]
        public async Task<IActionResult> GetObbContextDataTree()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                Dictionary<string, List<Dictionary<string, string>>> derContextData =
                    await magicOnionClient.Client.GetContextSellaTraderData("Obb-Combos-V1").ResponseAsync;


                return ApiResult(StatusCodes.Status200OK, derContextData);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }
        [HttpGet]
        [Route("GetObbContextDataTreeMobile")]
        public async Task<IActionResult> GetObbContextDataTreeMobile()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                Dictionary<string, List<Dictionary<string, string>>> derContextData =
                    await magicOnionClient.Client.GetContextSellaTraderData("Obb-Combos-V1").ResponseAsync;


                return ApiResult(StatusCodes.Status200OK, derContextData);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpGet]
        [Route("GetCwContextDataTree")]
        public async Task<IActionResult> GetCwContextDataTree()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                Dictionary<string, List<Dictionary<string, string>>> derContextData =
                    await magicOnionClient.Client.GetContextSellaTraderData("Cw-Combos-V1").ResponseAsync;

                return ApiResult(StatusCodes.Status200OK, derContextData);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }
        [HttpGet]
        [Route("GetCwContextDataTreeMobile")]
        public async Task<IActionResult> GetCwContextDataTreeMobile()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                Dictionary<string, List<Dictionary<string, string>>> derContextData =
                    await magicOnionClient.Client.GetContextSellaTraderData("Cw-Combos-V1").ResponseAsync;

                return ApiResult(StatusCodes.Status200OK, derContextData);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }
        #endregion

        [HttpGet]
        [Route("GetBondIssuersDescription")]
        public async Task<IActionResult> GetBondIssuersDescription()
        {
            try
            {
                MagicOnionClient<ICacheSearchService> magicOnionClient = new(_cacheProviderHost, _cacheProviderPort);
                List<string> issuers = await magicOnionClient.Client.GetBondIssuersDescription().ResponseAsync;

                if (issuers != null && issuers.Any())
                    return ApiResult(StatusCodes.Status200OK, new { Data = issuers });
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpPost]
        [Route("IsTradable")]
        public async Task<IActionResult> IsTradable([FromBody] StockModel stockModel)
        {
            try
            {
                // Enable CORS for this web methods - TODO - ripristinare?
                Response.Headers.Append("Access-Control-Allow-Origin", "*");
                BrokerProperties brokerProperties = await GetBrokerProperties();
                MagicOnionClient<ICacheService> handle = new(_cacheProviderHost, _cacheProviderPort);

                Stock stock = await handle.Client.GetStock(new StockKey(stockModel.MarketCode, stockModel.StockCode)).ResponseAsync;
                if (brokerProperties != null && stock != null)
                    return ApiResult(StatusCodes.Status200OK, new
                    {
                        isTradable = brokerProperties.IsTradable(stock.TradeGroupId)
                    });
                return ApiResult(StatusCodes.Status204NoContent, null);
            }
            catch (Exception exc)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, exc.Message);
            }
        }

        [HttpGet("GetLogos")]
        public async Task<IActionResult> GetLogos([FromQuery] string stockKeys)
        {
            MagicOnionClient<IContentService> handle = new(false, _cacheProviderHost, _cacheProviderPort);
            List<Logo> result = new List<Logo>();
            try
            {
                List<StockKey> sList = new();
                foreach (string key in stockKeys.Split(";"))
                {
                    sList.Add(new StockKey(key.Trim()));
                }

                result = await handle.Client.GetLogos(sList).ResponseAsync;
                var basePath = Path.Combine(@"wwwroot", "Loghi");

                foreach (var logo in result)
                {
                    logo.Dark = Path.Combine("loghi", logo.Dark);
                    logo.Light = Path.Combine("loghi", logo.Light);
                }

                return ApiResult(StatusCodes.Status200OK, result);
            }
            catch (Exception ex)
            {
                return ApiResult(StatusCodes.Status500InternalServerError, result);
            }
        }

        private async Task<List<string>> GetViewColumnsAsync(int customerId, string viewName)
        {
            return await GetViewColumnsAsync(customerId, null, viewName);
        }

        private async Task<List<string>> GetViewColumnsAsync(int customerId, string viewId, string viewName)
        {
            MagicOnionClient<IAccountService> handleLayout = new MagicOnionClient<IAccountService>(_customerProviderHost, _customerProviderPort);
            List<PersonalView> pviews = await handleLayout.Client.GetPersonalView(customerId, viewId, viewName, null, true).ResponseAsync;

            List<string> retVal = new List<string>();
            if (pviews != null && pviews.Any())
            {
                foreach (Col column in pviews.First().Columns)
                    retVal.Add(column.Name);
            }

            return retVal;
        }

        private List<FlowData> GetFlowData(List<StockCacheResponse> stockValues, List<string> cols, SpecificBrokerProperties brokerProperties)
        {
            List<FlowData> stockList = new List<FlowData>();

            MagicOnionClient<ICacheService> handle = new MagicOnionClient<ICacheService>(false, _cacheProviderHost, _cacheProviderPort);
            TimeZoneInfo timeZoneInfo = TimeZoneHelper.GetTimeZone();
            DateTime convertedDate = TimeZoneHelper.GetDateTimeWithTimeZone(DateTime.Now, timeZoneInfo);

            Dictionary<string, bool> marketHolidayInfo =
                handle.Client.IsMarketsHoliday(stockValues.Select(_ => _.MarketCode)
                                                            .Distinct().ToList(),
                                                convertedDate).ResponseAsync.Result;

            foreach (StockCacheResponse item in stockValues)
            {
                try
                {
                    bool isHoliday = false;
                    marketHolidayInfo.TryGetValue(item.MarketCode, out isHoliday);

                    InfoAbil infoAbil = InfoAbil.None;

                    if (item.Stock?.InfoGroupId != null &&
                       (brokerProperties?.InfoPermissions.TryGetValue(item.Stock.InfoGroupId, out var perm) ?? false))
                    {
                        infoAbil = perm.InfoAbil;
                    }

                    // TEMP: 0 gestito come 1 - JC (02/03/23)
                    //if (((InfoAbil)infoAbil) != InfoAbil.None) 
                    //{
                    bool hasFullInfo = (infoAbil & (InfoAbil.RealTime | InfoAbil.Push | InfoAbil.Graph | InfoAbil.Alarm)) != InfoAbil.None;
                    stockList.Add(item.GetFlowData(cols, brokerProperties?.IsTradable(item.Stock.TradeGroupId) ?? false, false, isHoliday, (short)infoAbil, hasFullInfo));
                    //}
                }
                catch (Exception ex)
                {
                    Logger.LogWarning(ex, "GetFlowData: error getting flow data for stock {m}.{s}", item.MarketCode, item.StockCode);
                }
            }

            return stockList;
        }

        private List<FlowData> GetFlowDataNoFilter(List<StockCacheResponse> stockValues, List<string> cols, SpecificBrokerProperties brokerProperties)
        {
            List<FlowData> stockList = new List<FlowData>();

            MagicOnionClient<ICacheService> handle = new MagicOnionClient<ICacheService>(false, _cacheProviderHost, _cacheProviderPort);
            TimeZoneInfo timeZoneInfo = TimeZoneHelper.GetTimeZone();
            DateTime convertedDate = TimeZoneHelper.GetDateTimeWithTimeZone(DateTime.Now, timeZoneInfo);

            Dictionary<string, bool> marketHolidayInfo =
                          handle.Client.IsMarketsHoliday(stockValues.Select(_ => _.MarketCode)
                                                                      .Distinct().ToList(),
                                                          convertedDate).ResponseAsync.Result;

            foreach (StockCacheResponse item in stockValues)
            {
                try
                {
                    InfoAbil infoAbil = InfoAbil.None;

                    if (item.Stock?.InfoGroupId != null &&
                       (brokerProperties?.InfoPermissions.TryGetValue(item.Stock.InfoGroupId, out var perm) ?? false))
                    {
                        infoAbil = perm.InfoAbil;
                    }

                    bool isHoliday = false;
                    marketHolidayInfo.TryGetValue(item.MarketCode, out isHoliday);

                    stockList.Add(item.GetFlowDataNew(cols, brokerProperties?.IsTradable(item.Stock.TradeGroupId) ?? false, false, isHoliday, (short)infoAbil));
                }
                catch (Exception ex)
                {
                    Logger.LogWarning(ex, "GetFlowData: error getting flow data for stock {m}.{s}", item.MarketCode, item.StockCode);
                }
            }

            return stockList;
        }

        //
        private List<FlowData> GetFlowData(List<StockCacheResponse> stockValues, List<string> cols, SpecificBrokerProperties brokerProperties, PersonalList pl)
        {
            List<FlowData> stockList = new List<FlowData>();

            MagicOnionClient<ICacheService> handle = new MagicOnionClient<ICacheService>(false, _cacheProviderHost, _cacheProviderPort);
            TimeZoneInfo timeZoneInfo = TimeZoneHelper.GetTimeZone();
            DateTime convertedDate = TimeZoneHelper.GetDateTimeWithTimeZone(DateTime.Now, timeZoneInfo);

            Dictionary<string, bool> marketHolidayInfo =
                            handle.Client.IsMarketsHoliday(stockValues.Select(_ => _.MarketCode)
                                                                        .Distinct().ToList(),
                                                            convertedDate).ResponseAsync.Result;

            List<Tuple<StockKey, (int Order, string Group)>> additionalFromPL = new();
            foreach (var pld in pl.Details)
            {
                additionalFromPL.Add(
                    new Tuple<StockKey, (int Order, string Group)>
                    (new StockKey(pld.MarketCode, pld.StockCode), new(pld.Order, pld.GroupName)));
            }

            foreach (StockCacheResponse item in stockValues.Distinct().ToList())
            {
                try
                {
                    if (item.Stock?.Key == ".")
                    {
                        continue;
                    }

                    bool isHoliday = false;
                    marketHolidayInfo.TryGetValue(item.MarketCode, out isHoliday);

                    InfoAbil infoAbil = InfoAbil.None;

                    if (item.Stock?.InfoGroupId != null &&
                        (brokerProperties?.InfoPermissions.TryGetValue(item.Stock.InfoGroupId, out var perm) ?? false))
                    {
                        infoAbil = perm.InfoAbil;
                    }

                    // TEMP: 0 gestito come 1 - JC (02/03/23)
                    //if (((InfoAbil)infoAbil) != InfoAbil.None) 
                    //{
                    bool hasFullInfo = (infoAbil & (InfoAbil.RealTime | InfoAbil.Push | InfoAbil.Graph | InfoAbil.Alarm)) != InfoAbil.None;

                    // TODO da rivedere

                    additionalFromPL.Where(_ => _.Item1 == item.Stock)
                                    .ToList()
                                    .ForEach(_ =>
                                        {
                                            var fd = item.GetFlowData(cols, brokerProperties?.IsTradable(item.Stock.TradeGroupId) ?? false, false, isHoliday, (short)infoAbil, hasFullInfo);

                                            fd.Values.Add("GROUP_NAME", _.Item2.Group);
                                            fd.Values.Add("ORDER", _.Item2.Order);

                                            stockList.Add(fd);
                                        }
                                    );

                    //}
                }
                catch (Exception ex)
                {
                    Logger.LogWarning(ex, "GetFlowData: error getting flow data for stock {m}.{s} in PersonalList {pl} of customer {c}", item.MarketCode, item.StockCode, pl.Id, pl.CustomerId);
                }
            }

            stockList = stockList
                .OrderBy(i => int.Parse(i.Values["ORDER"].ToString()))
                .ToList();

            return stockList;
        }

        private List<FlowData> GetFlowDataNoFilter(List<StockCacheResponse> stockValues, List<string> cols, SpecificBrokerProperties brokerProperties, PersonalList pl)
        {
            List<FlowData> stockList = new List<FlowData>();

            MagicOnionClient<ICacheService> handle = new MagicOnionClient<ICacheService>(false, _cacheProviderHost, _cacheProviderPort);
            TimeZoneInfo timeZoneInfo = TimeZoneHelper.GetTimeZone();
            DateTime convertedDate = TimeZoneHelper.GetDateTimeWithTimeZone(DateTime.Now, timeZoneInfo);

            Dictionary<string, bool> marketHolidayInfo =
                handle.Client.IsMarketsHoliday(stockValues.Select(_ => _.MarketCode)
                                                            .Distinct().ToList(),
                                                convertedDate).ResponseAsync.Result;

            List<Tuple<StockKey, (int Order, string Group)>> additionalFromPL = new();
            foreach (var pld in pl.Details)
            {
                additionalFromPL.Add(
                    new Tuple<StockKey, (int Order, string Group)>
                    (new StockKey(pld.MarketCode, pld.StockCode), new(pld.Order, pld.GroupName)));
            }

            foreach (StockCacheResponse item in stockValues.Distinct().ToList())
            {
                try
                {
                    if (item.Stock?.Key == ".")
                    {
                        continue;
                    }

                    InfoAbil infoAbil = InfoAbil.None;

                    if (item.Stock?.InfoGroupId != null &&
                       (brokerProperties?.InfoPermissions.TryGetValue(item.Stock.InfoGroupId, out var perm) ?? false))
                    {
                        infoAbil = perm.InfoAbil;
                    }

                    bool isHoliday = false;
                    marketHolidayInfo.TryGetValue(item.MarketCode, out isHoliday);

                    additionalFromPL.Where(_ => _.Item1 == item.Stock)
                                    .ToList()
                                    .ForEach(_ =>
                                        {
                                            var fd = item.GetFlowDataNew(cols, brokerProperties?.IsTradable(item.Stock.TradeGroupId) ?? false, false, isHoliday, (short)infoAbil);

                                            if (fd.Values.ContainsKey("GROUP_NAME"))
                                            {
                                                fd.Values["GROUP_NAME"] = _.Item2.Group;
                                            }
                                            else
                                            {
                                                fd.Values.Add("GROUP_NAME", _.Item2.Group);
                                            }

                                            if (fd.Values.ContainsKey("ORDER"))
                                            {
                                                fd.Values["ORDER"] = _.Item2.Order;
                                            }
                                            else
                                            {
                                                fd.Values.Add("ORDER", _.Item2.Order);
                                            }

                                            stockList.Add(fd);
                                        }
                                    );

                    //}
                }
                catch (Exception ex)
                {
                    Logger.LogWarning(ex, "GetFlowData: error getting flow data for stock {m}.{s} in PersonalList {pl} of customer {c}", item.MarketCode, item.StockCode, pl.Id, pl.CustomerId);
                }
            }

            stockList = stockList
                .OrderBy(i => int.Parse(i.Values["ORDER"].ToString()))
                .ToList();

            return stockList;
        }

        //private async Task<bool> CheckMarketHoliday(string marketCode)
        //{
        //    MagicOnionClient<ICacheService> handle = new MagicOnionClient<ICacheService>(false, _cacheProviderHost, _cacheProviderPort);
        //    TimeZoneInfo timeZoneInfo = TimeZoneHelper.GetTimeZone();
        //    DateTime convertedDate = TimeZoneHelper.GetDateTimeWithTimeZone(DateTime.Now, timeZoneInfo);
        //    handle.Client.WithDeadline(DateTime.Now + new TimeSpan(0, 10, 0));
        //    return await handle.Client.IsMarketHoliday(marketCode, convertedDate).ResponseAsync;
        //}
    }
}