using Microsoft.ApplicationInsights;
using Microsoft.Extensions.Logging;
using OT.Common.Extensions;
using OT.Common.Info.Series.Models;
using OT.Common.Models.Stocks;
using OT.Common.Models.Stocks.Info;
using OT.Probes.Probes;
using OT.Utils.Common.BufferManager.Fifo;
using OT.Utils.Common.Helpers.TimerHelper;
using OT.Utils.Common.Helpers.TimerHelper.Models;
using OT.Utils.Common.MessageBrokers.ServiceBusBrokers.Interfaces;
using Prometheus;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using Timer = System.Timers.Timer;

namespace SeriesDataWriterOnFile.Series
{
    internal class FifoHandler
    {
        private static readonly Gauge _writtenTicks = Metrics.CreateGauge("ot_fileseries_written_ticks", "Number of written ticks", new GaugeConfiguration { LabelNames = new[] { "MarketCode" } });
        private static readonly Gauge _writtenOnDisk = Metrics.CreateGauge("ot_fileseries_written_on_disk", "Number ticks on disk", new GaugeConfiguration { LabelNames = new[] { "MarketCode" } });

        private static readonly Gauge _queueLength = Metrics.CreateGauge("ot_fileseries_fileQueueLength", "Current Number Of writing queue");
        private static readonly Gauge _dequeueTime = Metrics.CreateGauge("ot_fileseries_fileDequeueTime", "Milliseconds to Flush ticks");
        private static readonly Gauge _durationFile = Metrics.CreateGauge("ot_fileseries_duration_write", "Tempo di scrittura tick su disco.", new GaugeConfiguration { LabelNames = new[] { "MarketCode" } });
        private static readonly Gauge _totalDelay = Metrics.CreateGauge("ot_fileseries_tickDelay", "Ritardo Tick.", new GaugeConfiguration { LabelNames = new[] { "MarketCode" } });

        private static readonly Gauge _writerStatus = Metrics.CreateGauge("ot_fileseries_writerStatus", "Stato scrittore Tick.", new GaugeConfiguration { LabelNames = new[] { "Status" } });

        private static readonly Gauge _receivedTickes = Metrics.CreateGauge("ot_fileseries_received_ticks", "Received ticks", new GaugeConfiguration { LabelNames = new[] { "MarketCode" } });
        private static readonly Gauge _nonWritable = Metrics.CreateGauge("ot_fileseries_nonwrittable_ticks", "Ticks che non vengono scritti senza price", new GaugeConfiguration { LabelNames = new[] { "MarketCode" } });
        private static readonly Gauge _receivedWrittableTickes = Metrics.CreateGauge("ot_fileseries_received_writtable_ticks", "Received writtable ticks", new GaugeConfiguration { LabelNames = new[] { "MarketCode" } });
       
        private static readonly Gauge _timeSkew = Metrics.CreateGauge("ot_fileseries_time_skew", "Ritardo di arrivo dell messaggio", new GaugeConfiguration { LabelNames = new[] { "MarketCode" } });
        private static readonly Gauge _queueFiles = Metrics.CreateGauge("ot_fileseries_queue_files", "Messaggi nella coda in attesa di essere scritti", new GaugeConfiguration { LabelNames = new[] { "MarketCode" } });

        public string _marketName;
        private string _tickFolder;
        private ConcurrentQueue<ConcurrentDictionary<string, MemoryStream>> writeQueue;
        public FifoDispatcher<TickToWrite, string> _fifoBuffer;
        //public FeedCounter<string> _writtenFeedCounter;
        private ILogger _logger;
        private LivenessProbeWeb _livenessProbe;
        private Thread dequeuer;
        private readonly IMessageBroker<List<InfoData>> _broker;

        private bool _toCheck = false;
        private readonly Timer _timer;
        private readonly TimerHelper _timerHelper;

        public FifoHandler(ILogger logger, TelemetryClient tc, LivenessProbeWeb livenessProbe, IMessageBroker<List<InfoData>> broker, string marketName, string tickFolder, int bufferSize)
        {
            _logger = logger;
            _livenessProbe = livenessProbe;
            _broker = broker;
            _marketName = marketName;
            _tickFolder = tickFolder;

            writeQueue = new ConcurrentQueue<ConcurrentDictionary<string, MemoryStream>>();
            //_writtenFeedCounter = new FeedCounter<string>(new List<string>() { _marketName });

            _fifoBuffer = new FifoDispatcher<TickToWrite, string>(_logger);
            _fifoBuffer.AddConsumer(_marketName, Consumer, bufferSize);
            _timerHelper = new(tc, "SeriesDataWriterOnFile");
            _timer = _timerHelper.GetTimer(5000, UpdateStatistics);
        }

        public async Task Start()
        {
            dequeuer = new Thread(Dequeue);
            dequeuer.Start();
            await _broker.FollowQueueOrTopicAsync(ProcessData);
        }

        public Task ProcessData(List<InfoData> infoDatas)
        {
            foreach (InfoData infoData in infoDatas)
            {
                _receivedTickes.WithLabels(infoData.MarketCode).Inc();

                string key = infoData.MarketCode;
                if (infoData.Price != null)
                {
                    ProcessPrice(key, infoData);
                }
                else
                {
                    _nonWritable.WithLabels(infoData.MarketCode).Inc();
                }
            }
            return Task.CompletedTask;
        }
        private void ProcessPrice(string key, InfoData infoData)
        {
            if (infoData.Price.Last.HasValue && infoData.Price.Timestamp.HasValue)
            {
                _receivedWrittableTickes.WithLabels(infoData.MarketCode).Inc();

                if (_toCheck)
                {
                    _toCheck = false;

                    DateTime now = DateTime.Now;
                    double difference = now.Subtract(infoData.Price.Timestamp.Value).TotalMilliseconds;
                    if (difference < 0 || difference >= 3600000)//se la differenza è < 0 ms o >= 1 ora scrivo log
                    {
                        _logger.LogWarning($"ProcessPrice ErrorDelay: {infoData.MarketCode} {now} - {infoData.Price.Timestamp.Value} = {difference} ms");
                    }
                    _timeSkew.WithLabels(infoData.MarketCode).Set(difference);
                }

                try
                {
                    TickToWrite tw = new TickToWrite(infoData);

                    EnqueueAsync(key, tw).GetAwaiter().GetResult();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Errore processamento price: {MarketCode}.{StockCode}, {PriceTimestamp},{PriceLast},{PriceQtaLastTrade}",
                        infoData.MarketCode, infoData.StockCode, infoData.Price.Timestamp, infoData.Price.Last, infoData.Price.QtaLastTrade);
                }
            }
            else
            {
                _nonWritable.WithLabels(infoData.MarketCode).Inc();
            }
        }

        private Task EnqueueAsync(string key, TickToWrite tick)
        {
            return _fifoBuffer.EnqueueAsync(key, tick);
        }

        public void ResetStatistics()
        {
            foreach (string[] label in _writtenTicks.GetAllLabelValues().ToList())
                _writtenTicks.WithLabels(label).Set(0);
            foreach (string[] label in _writtenOnDisk.GetAllLabelValues().ToList())
                _writtenOnDisk.WithLabels(label).Set(0);
            foreach (string[] label in _receivedTickes.GetAllLabelValues().ToList())
                _receivedTickes.WithLabels(label).Set(0);
            foreach (string[] label in _nonWritable.GetAllLabelValues().ToList())
                _nonWritable.WithLabels(label).Set(0);
            foreach (string[] label in _receivedWrittableTickes.GetAllLabelValues().ToList())
                _receivedWrittableTickes.WithLabels(label).Set(0);

        }

        private TimerMetricsModel UpdateStatistics(ElapsedEventArgs e)
        {
            // Do not remove, this bool is used to set the _timeSkw metrics
            _toCheck = true;
            try
            {

                // This object will be used by the private method of TimerHelper class to save the Application Insights metrics
                return new TimerMetricsModel(false)
                {
                    //TODO Metrica impostata a false per non essere inviata, fino a quando non si decideranno i valori da registrare

                    Properties = {
                    { "Type", "BackgroundJob" },
                    { "Description", "A new UpdateStatistics job was executed" },
                    { "ActionName", "UpdateStatistics" }
                },
                    Metrics = {
                   //TODO Inserire qui le metriche
                    }
                };
            }
            catch (Exception)
            {
                return null;
            }
        }

        #region Consumer
        private async Task Consumer(string key, List<TickToWrite> lst)
        {
            IEnumerable<IGrouping<StockKey, TickToWrite>> group = lst.GroupBy(pk => pk.StockKey);
            await Task.Run(() =>
            {
                Parallel.ForEach(group, new ParallelOptions { MaxDegreeOfParallelism = 20 }, (g) =>
                {
                    WriteTick(key, g);
                });
            });
        }
        private void WriteTick(string key, IGrouping<StockKey, TickToWrite> ticks)
        {
            Int32 writtenTicks = 0;

            try
            {
                string write = Environment.GetEnvironmentVariable("WRITE");
                if (write == "NO")
                {
                    foreach (Tick tick in ticks)
                    {
                        writtenTicks++;
                    }
                }
                else
                {
                    var temp = new ConcurrentDictionary<string, MemoryStream>();
                    MemoryStream ms = temp.GetOrAdd(ticks.Key.ToString(), new MemoryStream());
                    foreach (Tick tick in ticks)
                    {
                        ms.Write(tick.TimestampTicks.Value);
                        ms.Write(tick.Price.Value);
                        ms.Write(tick.Volume.Value);
                        ms.Write((byte)tick.Type);

                        writtenTicks++;
                    }

                    writeQueue.Enqueue(temp);
                }

                _writtenTicks.WithLabels(ticks.Key.MarketCode).Inc(writtenTicks);
                _livenessProbe.Push_OK("WriteTick");
            }
            catch (IOException ioEx)
            {
                try
                {
                    // Fallback che dovrebbe servire solo durante la fase di consolidamento, quando il file potrebbe essere in lock in attesa di essere spostato
                    if (!RetryWriteTick(key, ticks, 0))
                        throw new Exception("Numero massimo di tentativi di recupero scrittura del tick superati!", ioEx);
                    else
                    {

                        _logger.LogWarning("Errore IO in scrittura del ticks {TicksKey } correttamente recuperato { ioEx }", ticks.Key, ioEx);
                    }
                    _livenessProbe.Push_OK("WriteTick");
                }
                catch (Exception ex)
                {
                    _livenessProbe.Push_KO("WriteTick", ex);
                    _logger.LogError(ex, "Errore in retry scrittura file ticks {TicksKey}", ticks.Key);

                }
            }
            catch (Exception ex)
            {
                _livenessProbe.Push_KO("WriteTick", ex);
                _logger.LogError(ex, "Errore in scrittura file ticks{TicksKey}", ticks.Key);
            }

            //_writtenFeedCounter.Increment(key, writtenTicks);
        }
        private Boolean RetryWriteTick(string key, IGrouping<StockKey, TickToWrite> ticks, Int32 iteration)
        {
            Int32 writtenTicks = 0;
            Boolean ret = false;
            try
            {
                Thread.Sleep(10);
                if (iteration < 5)
                {
                    using (FileStream fs = new FileStream(Path.Combine(_tickFolder, ticks.Key.ToString() + ".bin"), FileMode.OpenOrCreate, FileAccess.Write, FileShare.Read))
                    {
                        fs.Seek(0, SeekOrigin.End);

                        foreach (Tick tick in ticks)
                        {
                            TickToWrite.Write(tick, fs);
                            writtenTicks++;
                        }
                    }
                    ret = true;
                }
                _livenessProbe.Push_OK("RetryWriteTick");
            }
            catch (IOException ex)
            {
                _livenessProbe.Push_KO("RetryWriteTick", ex);
                _logger.LogWarning("Errore writing {TicksKey } correttamente recuperato { iteration }", ticks.Key, iteration);
                ret = RetryWriteTick(key, ticks, iteration + 1);
            }

            _writtenTicks.WithLabels(ticks.Key.MarketCode).Inc(writtenTicks);
            //_writtenFeedCounter.Increment(key, writtenTicks);

            return ret;
        }
        #endregion

        #region Dequeue
        private void Dequeue()
        {
            while (true)
            {
                Stopwatch sw = new Stopwatch();
                sw.Start();
                _queueLength.Set(writeQueue.Count);
                _writerStatus.WithLabels("queueLength").Set(writeQueue.Count);

                int bumpCounter = 0;
                if (writeQueue.TryDequeue(out ConcurrentDictionary<string, MemoryStream> cd))
                {
                    sw.Restart();
                    bumpCounter += cd.Count;
                    _writerStatus.WithLabels("bumping").Set(cd.Count);

                    while (true)
                    { 
                        if (writeQueue.TryDequeue(out ConcurrentDictionary<string, MemoryStream> cdn))
                        {
                            cd = MergeConcurrentDictionary(cd, cdn);
                            bumpCounter += cdn.Count;
                        }
                        else
                        {
                            break;
                        }
                    }
                    _writerStatus.WithLabels("bumped").Set(bumpCounter);

                    if (bumpCounter > 0)
                        _logger.LogInformation("{0} - Bumped {1} Remaining {2}", DateTime.Now, bumpCounter, writeQueue.Count);

                    if (!cd.IsEmpty)
                    {
                        sw.Restart();
                        FlushCache(cd);

                        if (cd.Count != 0)
                        {
                            double tickWriteTime = sw.ElapsedMilliseconds / cd.Count;
                            _dequeueTime.Set(tickWriteTime);
                        }

                        _logger.LogInformation("{0} writeQueue {1} - written {2} - {3}", DateTime.Now, writeQueue.Count, cd.Count, sw.Elapsed);
                        _writerStatus.WithLabels("flushed").Set(cd.Count);
                    }
                }
                else
                {
                    Thread.Sleep(100);
                }
            }
        }

        private ConcurrentDictionary<string, MemoryStream> MergeConcurrentDictionary(ConcurrentDictionary<string, MemoryStream> cd1, ConcurrentDictionary<string, MemoryStream> cd2)
        {
            foreach (KeyValuePair<string, MemoryStream> entry in cd2)
            {
                if (cd1.ContainsKey(entry.Key))
                {
                    entry.Value.WriteTo(cd1[entry.Key]);
                }
                else
                {
                    cd1.AddOrUpdate(entry.Key, entry.Value, (key, oldValue) => oldValue);
                }
            }
            return cd1;
        }

        private void FlushCache(ConcurrentDictionary<string, MemoryStream> writeCache)
        {
            int fileCount = 0;
            long ticksCount = 0;

            Stopwatch sw = new Stopwatch();
            sw.Start();

            _writerStatus.WithLabels("flushing").Set(writeCache.Count);

            //questo è il motivo per cui il bumping porta vantaggi, però su valori grandi di MaxDegreeOfParallelism cosa succede?
            ParallelOptions options = new ParallelOptions { MaxDegreeOfParallelism = writeCache.Count };
            Parallel.ForEach(writeCache, options, cacheEntry =>
            {
                if (cacheEntry.Value != null)
                {
                    Stopwatch sw2 = new Stopwatch();
                    sw2.Start();

                    string marketCode = cacheEntry.Key.Substring(0, cacheEntry.Key.IndexOf("."));
                    long ticks = cacheEntry.Value.Length / Tick.ByteSize();

                    for (int i = 0; i < 10; i++)
                    {
                        try
                        {
                            using (FileStream fs = new FileStream(Path.Combine(_tickFolder, cacheEntry.Key + ".bin"), FileMode.Append, FileAccess.Write, FileShare.Read))
                            {
                                cacheEntry.Value.WriteTo(fs);
                                fileCount++;

                                try
                                {
                                    cacheEntry.Value.Seek(-Tick.ByteSize(), SeekOrigin.End);
                                    Tick tick = TickToWrite.Read(cacheEntry.Value);
                                    cacheEntry.Value.Close();

                                    DateTime ts = new DateTime((long)tick.TimestampTicks);
                                    DateTime now = DateTime.Now;
                                    TimeSpan delay = now - ts;

                                    if (delay.TotalMilliseconds < 0 || delay.TotalMilliseconds >= 3600000)//se la differenza è < 0 ms o >= 1 ora scrivo log
                                    {
                                        _logger.LogWarning($"FlushCache ErrorDelay: {marketCode} {now} - {ts} = {delay.TotalMilliseconds} ms");
                                    }

                                    _totalDelay.WithLabels(marketCode).Set(delay.TotalMilliseconds);
                                }
                                catch(Exception e)
                                {
                                    cacheEntry.Value.Close();
                                    _logger.LogWarning("Error writing delay  {0}, the process has Exception {2}", cacheEntry.Key, e);
                                }
                                break;
                            }
                        }
                        catch (Exception e)
                        {
                            cacheEntry.Value.Close();
                            _logger.LogWarning("Retrying writing file {0} attempt {1}, the process has Exception {2}", cacheEntry.Key, i, e);
                        }
                    }
                    _writtenOnDisk.WithLabels(marketCode).Inc(ticks);
                    ticksCount += ticks;
                    _durationFile.WithLabels(marketCode).Set(sw2.Elapsed.TotalMilliseconds / ticks);
                }
                else
                {
                    _logger.LogError("cacheEntry NULL per {CacheEntryKey}", cacheEntry.Key);
                }
            });

            if (fileCount > 0 & ticksCount > 0)
                _logger.LogInformation("{0} FlushCache Files {1} {2} - Ticks {3} {4} Elapsed {5}", DateTime.Now, fileCount, sw.Elapsed / fileCount, ticksCount, sw.Elapsed / ticksCount, sw.Elapsed);
        }
        #endregion
    }
}
