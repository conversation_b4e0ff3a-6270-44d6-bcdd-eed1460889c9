#!/usr/bin/env python3
"""
Performance Analyzer Tool for OT-O2x Codebase
Scans C# source files for performance-critical object allocation patterns
"""

import os
import re
import json
import argparse
from pathlib import Path
from typing import List, Dict, Tuple, Set
from dataclasses import dataclass, asdict
from collections import defaultdict
from datetime import datetime

@dataclass
class PerformanceIssue:
    file_path: str
    line_number: int
    line_content: str
    issue_type: str
    severity: str
    description: str
    suggestion: str

class PerformanceAnalyzer:
    def __init__(self, root_path: str):
        self.root_path = Path(root_path)
        self.issues: List[PerformanceIssue] = []

        # Critical patterns that cause performance issues
        self.critical_patterns = {
            'stopwatch_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+Stopwatch\s*\(',
                'severity': 'CRITICAL',
                'description': 'Stopwatch allocation in loop',
                'suggestion': 'Use shared Stopwatch with timestamp differences'
            },
            'task_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+Task\s*[(<]',
                'severity': 'CRITICAL',
                'description': 'Task allocation in loop',
                'suggestion': 'Use Task.Run with shared delegate or TaskFactory'
            },
            'thread_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+Thread\s*\(',
                'severity': 'CRITICAL',
                'description': 'Thread allocation in loop',
                'suggestion': 'Use ThreadPool or reuse threads'
            },
            'memorystream_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+MemoryStream\s*\(',
                'severity': 'HIGH',
                'description': 'MemoryStream allocation in loop',
                'suggestion': 'Use object pooling or reuse MemoryStream instances'
            },
            'stringbuilder_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+StringBuilder\s*\(',
                'severity': 'HIGH',
                'description': 'StringBuilder allocation in loop',
                'suggestion': 'Reuse StringBuilder instance with Clear() method'
            },
            'dictionary_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+(Dictionary|ConcurrentDictionary)\s*<',
                'severity': 'HIGH',
                'description': 'Dictionary allocation in loop',
                'suggestion': 'Move dictionary creation outside loop or use object pooling'
            },
            'list_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+List\s*<',
                'severity': 'MEDIUM',
                'description': 'List allocation in loop',
                'suggestion': 'Reuse List instance with Clear() method'
            },
            'datetime_now_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*DateTime\.Now\b',
                'severity': 'HIGH',
                'description': 'DateTime.Now in loop',
                'suggestion': 'Cache DateTime.Now outside loop'
            },
            'datetime_now_repeated': {
                'pattern': r'DateTime\.Now.*DateTime\.Now',
                'severity': 'MEDIUM',
                'description': 'Multiple DateTime.Now calls',
                'suggestion': 'Cache DateTime.Now in a variable'
            },
            'new_timespan_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+TimeSpan\s*\(',
                'severity': 'MEDIUM',
                'description': 'TimeSpan allocation in loop',
                'suggestion': 'Use TimeSpan static methods or cache instances'
            },
            'string_concat_in_loop': {
                'pattern': r'(for|while|foreach).*?{[^}]*\w+\s*\+=\s*["\']',
                'severity': 'HIGH',
                'description': 'String concatenation in loop',
                'suggestion': 'Use StringBuilder for string concatenation in loops'
            },
            'string_format_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*string\.Format\(',
                'severity': 'HIGH',
                'description': 'string.Format in loop',
                'suggestion': 'Use StringBuilder or string interpolation'
            },
            'reflection_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*(GetType|GetMethod|GetProperty|GetField)\(',
                'severity': 'HIGH',
                'description': 'Reflection in loop',
                'suggestion': 'Cache reflection results outside loop'
            },
            'concat_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*\.Concat\(',
                'severity': 'HIGH',
                'description': 'LINQ Concat in loop',
                'suggestion': 'Use AddRange or collect items first'
            }
        }

        # Single-line patterns for simpler detection
        self.simple_patterns = {
            'new_stopwatch': {
                'pattern': r'new\s+Stopwatch\s*\(',
                'severity': 'MEDIUM',
                'description': 'Stopwatch allocation',
                'suggestion': 'Consider using shared Stopwatch for high-frequency code'
            },
            'new_exception_in_method': {
                'pattern': r'throw\s+new\s+\w*Exception\s*\(',
                'severity': 'LOW',
                'description': 'Exception allocation',
                'suggestion': 'Pre-allocate common exceptions if thrown frequently'
            },
            'tolist_unnecessary': {
                'pattern': r'\.ToList\(\)\.ToList\(\)',
                'severity': 'MEDIUM',
                'description': 'Redundant ToList() calls',
                'suggestion': 'Remove redundant ToList() calls'
            },
            'multiple_where_clauses': {
                'pattern': r'\.Where\([^)]+\)\.Where\(',
                'severity': 'HIGH',
                'description': 'Multiple LINQ Where clauses',
                'suggestion': 'Combine Where conditions with && operator'
            },
            'tolist_before_count': {
                'pattern': r'\.ToList\(\)\.Count\b',
                'severity': 'HIGH',
                'description': 'ToList().Count instead of Count()',
                'suggestion': 'Use Count() directly on IEnumerable'
            },
            'count_greater_than_zero': {
                'pattern': r'\.Count\(\)\s*>\s*0',
                'severity': 'MEDIUM',
                'description': 'Count() > 0 instead of Any()',
                'suggestion': 'Use Any() for existence checks'
            },
            'string_split_repeated': {
                'pattern': r'\.Split\([^)]+\).*\.Split\(',
                'severity': 'MEDIUM',
                'description': 'Multiple Split operations',
                'suggestion': 'Cache split result in variable'
            },
            'gettype_repeated': {
                'pattern': r'\.GetType\(\).*\.GetType\(\)',
                'severity': 'MEDIUM',
                'description': 'Multiple GetType() calls',
                'suggestion': 'Cache Type in variable'
            },
            'array_resize': {
                'pattern': r'Array\.Resize\s*\(',
                'severity': 'HIGH',
                'description': 'Array.Resize operation',
                'suggestion': 'Use List<T> or pre-allocate correct size'
            },
            'filestream_not_disposed': {
                'pattern': r'new\s+FileStream\([^)]+\)(?!.*using)',
                'severity': 'HIGH',
                'description': 'FileStream without using statement',
                'suggestion': 'Wrap in using statement or use File.ReadAllText/WriteAllText'
            },
            'file_exists_repeated': {
                'pattern': r'File\.Exists\([^)]+\).*File\.Exists\(',
                'severity': 'MEDIUM',
                'description': 'Multiple File.Exists calls',
                'suggestion': 'Cache File.Exists result'
            },
            'boxing_tostring': {
                'pattern': r'(int|long|double|float|decimal).*\.ToString\(\)',
                'severity': 'LOW',
                'description': 'Potential boxing with ToString()',
                'suggestion': 'Use ToString() with format or cache string representations'
            },
            'linq_orderby_multiple': {
                'pattern': r'\.OrderBy\([^)]+\)\.OrderBy\(',
                'severity': 'MEDIUM',
                'description': 'Multiple OrderBy calls',
                'suggestion': 'Use ThenBy for secondary sorting'
            },
            'select_tolist': {
                'pattern': r'\.Select\([^)]+\)\.ToList\(\)\.Select\(',
                'severity': 'HIGH',
                'description': 'Select().ToList().Select() chain',
                'suggestion': 'Combine Select operations or avoid intermediate ToList()'
            }
        }

    def scan_file(self, file_path: Path) -> None:
        """Scan a single C# file for performance issues"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                lines = content.split('\n')

            # Check for complex patterns (multi-line)
            self._check_complex_patterns(file_path, content, lines)

            # Check for simple patterns (single-line)
            self._check_simple_patterns(file_path, lines)

        except Exception as e:
            print(f"Error scanning {file_path}: {e}")

    def _check_complex_patterns(self, file_path: Path, content: str, lines: List[str]) -> None:
        """Check for complex multi-line patterns"""
        for pattern_name, pattern_info in self.critical_patterns.items():
            matches = re.finditer(pattern_info['pattern'], content, re.MULTILINE | re.DOTALL)
            for match in matches:
                # Find line number
                line_num = content[:match.start()].count('\n') + 1
                line_content = lines[line_num - 1].strip() if line_num <= len(lines) else ""

                self.issues.append(PerformanceIssue(
                    file_path=str(file_path.relative_to(self.root_path)),
                    line_number=line_num,
                    line_content=line_content,
                    issue_type=pattern_name,
                    severity=pattern_info['severity'],
                    description=pattern_info['description'],
                    suggestion=pattern_info['suggestion']
                ))

    def _check_simple_patterns(self, file_path: Path, lines: List[str]) -> None:
        """Check for simple single-line patterns"""
        for line_num, line in enumerate(lines, 1):
            for pattern_name, pattern_info in self.simple_patterns.items():
                if re.search(pattern_info['pattern'], line):
                    self.issues.append(PerformanceIssue(
                        file_path=str(file_path.relative_to(self.root_path)),
                        line_number=line_num,
                        line_content=line.strip(),
                        issue_type=pattern_name,
                        severity=pattern_info['severity'],
                        description=pattern_info['description'],
                        suggestion=pattern_info['suggestion']
                    ))

    def scan_directory(self, extensions: Set[str] = {'.cs'}) -> None:
        """Scan all files in directory with given extensions"""
        for file_path in self.root_path.rglob('*'):
            if file_path.is_file() and file_path.suffix in extensions:
                # Skip certain directories
                if any(skip in str(file_path) for skip in ['bin', 'obj', 'packages', '.git', 'node_modules']):
                    continue
                self.scan_file(file_path)

    def generate_report(self) -> Dict:
        """Generate comprehensive performance report"""
        # Group issues by severity
        by_severity = defaultdict(list)
        by_file = defaultdict(list)
        by_type = defaultdict(list)

        for issue in self.issues:
            by_severity[issue.severity].append(issue)
            by_file[issue.file_path].append(issue)
            by_type[issue.issue_type].append(issue)

        # Calculate statistics
        total_issues = len(self.issues)
        critical_count = len(by_severity['CRITICAL'])
        high_count = len(by_severity['HIGH'])
        medium_count = len(by_severity['MEDIUM'])
        low_count = len(by_severity['LOW'])

        # Find most problematic files
        most_problematic = sorted(by_file.items(), key=lambda x: len(x[1]), reverse=True)[:10]

        return {
            'summary': {
                'total_issues': total_issues,
                'critical_issues': critical_count,
                'high_issues': high_count,
                'medium_issues': medium_count,
                'low_issues': low_count,
                'files_scanned': len(by_file),
                'most_problematic_files': [
                    {'file': file, 'issue_count': len(issues)}
                    for file, issues in most_problematic
                ]
            },
            'issues_by_severity': {
                severity: [asdict(issue) for issue in issues]
                for severity, issues in by_severity.items()
            },
            'issues_by_type': {
                issue_type: len(issues)
                for issue_type, issues in by_type.items()
            }
        }

    def save_report(self, output_file: str) -> None:
        """Save report to JSON file"""
        report = self.generate_report()
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

    def generate_markdown_report(self) -> str:
        """Generate concise markdown report"""
        report = self.generate_report()
        summary = report['summary']

        md = f"""# 🔍 Performance Analysis Report

## 📊 Executive Summary
- **Total Issues**: {summary['total_issues']}
- **Critical**: {summary['critical_issues']} 🚨
- **High Priority**: {summary['high_issues']} ⚠️
- **Files Scanned**: {summary['files_scanned']}

## 🚨 Critical Issues (Immediate Action Required)
"""

        # Critical issues by type
        critical_types = {}
        high_types = {}
        for issue in self.issues:
            if issue.severity == 'CRITICAL':
                critical_types[issue.issue_type] = critical_types.get(issue.issue_type, 0) + 1
            elif issue.severity == 'HIGH':
                high_types[issue.issue_type] = high_types.get(issue.issue_type, 0) + 1

        if critical_types:
            for issue_type, count in sorted(critical_types.items(), key=lambda x: x[1], reverse=True):
                md += f"- **{issue_type.replace('_', ' ').title()}**: {count} occurrences\n"
        else:
            md += "- No critical issues found\n"

        md += f"""
## ⚠️ High Priority Issues
"""
        for issue_type, count in sorted(high_types.items(), key=lambda x: x[1], reverse=True)[:5]:
            md += f"- **{issue_type.replace('_', ' ').title()}**: {count} occurrences\n"

        # Critical files
        critical_files = self.get_critical_files()
        if critical_files:
            md += f"""
## 🔥 Critical Files ({len(critical_files)})
"""
            for file in critical_files[:10]:
                md += f"- `{file}`\n"

        # Hot paths
        hotpaths = self.get_hotpath_indicators()
        if hotpaths:
            md += f"""
## 🎯 Hot Paths ({len(hotpaths)} files with 3+ performance indicators)
"""
            for file, issues in sorted(hotpaths.items(), key=lambda x: len(x[1]), reverse=True)[:5]:
                md += f"- `{file}` ({len(issues)} indicators)\n"

        # Top problematic files
        md += f"""
## 📈 Most Problematic Files
"""
        for i, file_info in enumerate(summary['most_problematic_files'][:5], 1):
            md += f"{i}. `{file_info['file']}` ({file_info['issue_count']} issues)\n"

        # Action plan
        md += f"""
## 🎯 Immediate Action Plan

### Phase 1: Critical (Today)
"""
        if critical_types:
            for issue_type, count in list(sorted(critical_types.items(), key=lambda x: x[1], reverse=True))[:3]:
                suggestion = self._get_suggestion_for_type(issue_type)
                md += f"- Fix **{count}** instances of `{issue_type}`\n  - {suggestion}\n"

        md += f"""
### Phase 2: High Priority (This Week)
"""
        for issue_type, count in list(sorted(high_types.items(), key=lambda x: x[1], reverse=True))[:3]:
            suggestion = self._get_suggestion_for_type(issue_type)
            md += f"- Address **{count}** instances of `{issue_type}`\n  - {suggestion}\n"

        md += f"""
## 💡 Key Recommendations
1. **Implement Object Pooling** for frequently allocated objects
2. **Cache DateTime.Now** in hot paths
3. **Use Shared Stopwatch** for timing operations
4. **Optimize LINQ chains** to reduce intermediate allocations
5. **Monitor GC pressure** after fixes

---
*Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        return md

    def _get_suggestion_for_type(self, issue_type: str) -> str:
        """Get suggestion for specific issue type"""
        suggestions = {
            'stopwatch_in_loop': 'Use shared Stopwatch with timestamp differences',
            'task_in_loop': 'Use Task.Run with shared delegate or TaskFactory',
            'thread_in_loop': 'Use ThreadPool or reuse threads',
            'dictionary_in_loop': 'Move dictionary creation outside loop or use object pooling',
            'datetime_now_in_loop': 'Cache DateTime.Now outside loop',
            'memorystream_in_loop': 'Use object pooling or reuse MemoryStream instances',
            'stringbuilder_in_loop': 'Reuse StringBuilder instance with Clear() method',
            'string_concat_in_loop': 'Use StringBuilder for string concatenation in loops',
            'list_in_loop': 'Reuse List instance with Clear() method',
            'tolist_before_count': 'Use Count() directly on IEnumerable',
            'multiple_where_clauses': 'Combine Where conditions with && operator'
        }
        return suggestions.get(issue_type, 'Review and optimize allocation pattern')

    def save_markdown_report(self, output_file: str) -> None:
        """Save markdown report to file"""
        md_content = self.generate_markdown_report()
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(md_content)

    def get_critical_files(self) -> List[str]:
        """Get list of files with critical performance issues"""
        critical_files = set()
        for issue in self.issues:
            if issue.severity == 'CRITICAL':
                critical_files.add(issue.file_path)
        return sorted(list(critical_files))

    def get_hotpath_indicators(self) -> Dict[str, List[PerformanceIssue]]:
        """Identify potential hot paths based on issue patterns"""
        hotpaths = defaultdict(list)

        # Files with multiple loop-related issues are likely hot paths
        for issue in self.issues:
            if any(keyword in issue.issue_type for keyword in ['_in_loop', 'repeated', 'multiple']):
                hotpaths[issue.file_path].append(issue)

        # Filter to files with 3+ hot path indicators
        return {file: issues for file, issues in hotpaths.items() if len(issues) >= 3}

    def print_summary(self) -> None:
        """Print summary to console"""
        report = self.generate_report()
        summary = report['summary']

        print("🔍 PERFORMANCE ANALYSIS REPORT")
        print("=" * 50)
        print(f"📊 Total Issues Found: {summary['total_issues']}")
        print(f"🚨 Critical: {summary['critical_issues']}")
        print(f"⚠️  High: {summary['high_issues']}")
        print(f"🔶 Medium: {summary['medium_issues']}")
        print(f"ℹ️  Low: {summary['low_issues']}")
        print(f"📁 Files Scanned: {summary['files_scanned']}")

        # Show critical files
        critical_files = self.get_critical_files()
        if critical_files:
            print(f"\n🚨 CRITICAL FILES ({len(critical_files)}):")
            for file in critical_files[:10]:  # Show top 10
                print(f"  • {file}")

        # Show potential hot paths
        hotpaths = self.get_hotpath_indicators()
        if hotpaths:
            print(f"\n🔥 POTENTIAL HOT PATHS ({len(hotpaths)}):")
            for file, issues in sorted(hotpaths.items(), key=lambda x: len(x[1]), reverse=True)[:5]:
                print(f"  • {file} ({len(issues)} indicators)")

        print("\n🏆 TOP 5 MOST PROBLEMATIC FILES:")
        for i, file_info in enumerate(summary['most_problematic_files'][:5], 1):
            print(f"{i}. {file_info['file']} ({file_info['issue_count']} issues)")

        print("\n📈 ISSUES BY TYPE:")
        for issue_type, count in sorted(report['issues_by_type'].items(), key=lambda x: x[1], reverse=True):
            severity_icon = "🚨" if any(i.severity == 'CRITICAL' for i in self.issues if i.issue_type == issue_type) else \
                           "⚠️" if any(i.severity == 'HIGH' for i in self.issues if i.issue_type == issue_type) else "🔶"
            print(f"  {severity_icon} {issue_type}: {count}")

    def generate_refactoring_suggestions(self) -> Dict[str, str]:
        """Generate specific refactoring suggestions for common patterns"""
        suggestions = {
            'stopwatch_in_loop': '''
// ❌ BEFORE (Performance Issue)
for (int i = 0; i < items.Count; i++) {
    Stopwatch sw = new Stopwatch();
    sw.Start();
    // ... processing ...
    sw.Stop();
    metrics.Set(sw.ElapsedMilliseconds);
}

// ✅ AFTER (Optimized)
private static readonly Stopwatch _sharedStopwatch = Stopwatch.StartNew();

for (int i = 0; i < items.Count; i++) {
    long startTicks = _sharedStopwatch.ElapsedTicks;
    // ... processing ...
    long elapsedTicks = _sharedStopwatch.ElapsedTicks - startTicks;
    long elapsedMs = (elapsedTicks * 1000) / Stopwatch.Frequency;
    metrics.Set(elapsedMs);
}''',

            'memorystream_in_loop': '''
// ❌ BEFORE (Performance Issue)
foreach (var item in items) {
    using var ms = new MemoryStream();
    // ... write to stream ...
}

// ✅ AFTER (Optimized with Object Pool)
private static readonly ObjectPool<MemoryStream> _streamPool =
    new DefaultObjectPool<MemoryStream>(new MemoryStreamPooledObjectPolicy());

foreach (var item in items) {
    var ms = _streamPool.Get();
    try {
        ms.SetLength(0); // Reset stream
        // ... write to stream ...
    } finally {
        _streamPool.Return(ms);
    }
}''',

            'string_concat_in_loop': '''
// ❌ BEFORE (Performance Issue)
string result = "";
foreach (var item in items) {
    result += item.ToString() + ", ";
}

// ✅ AFTER (Optimized)
var sb = new StringBuilder(items.Count * 10); // Pre-size if possible
foreach (var item in items) {
    sb.Append(item.ToString()).Append(", ");
}
string result = sb.ToString();''',

            'tolist_before_count': '''
// ❌ BEFORE (Performance Issue)
if (items.Where(x => x.IsActive).ToList().Count > 0) {
    // ...
}

// ✅ AFTER (Optimized)
if (items.Where(x => x.IsActive).Any()) {
    // ...
}'''
        }
        return suggestions

def main():
    parser = argparse.ArgumentParser(description='Analyze C# codebase for performance issues')
    parser.add_argument('--root', default='.', help='Root directory to scan')
    parser.add_argument('--output', default='performance_report.json', help='Output JSON file')
    parser.add_argument('--markdown', default='performance_report.md', help='Output Markdown file')
    parser.add_argument('--summary-only', action='store_true', help='Print only summary')
    parser.add_argument('--show-examples', action='store_true', help='Show refactoring examples')

    args = parser.parse_args()

    analyzer = PerformanceAnalyzer(args.root)
    print(f"🔍 Scanning {args.root} for performance issues...")

    analyzer.scan_directory()
    analyzer.print_summary()

    if args.show_examples:
        print("\n💡 REFACTORING EXAMPLES:")
        print("=" * 50)
        suggestions = analyzer.generate_refactoring_suggestions()
        for pattern, example in suggestions.items():
            if any(issue.issue_type == pattern for issue in analyzer.issues):
                print(f"\n🔧 {pattern.upper().replace('_', ' ')}:")
                print(example)

    if not args.summary_only:
        analyzer.save_report(args.output)
        analyzer.save_markdown_report(args.markdown)
        print(f"\n📄 Detailed report saved to: {args.output}")
        print(f"📋 Markdown report saved to: {args.markdown}")

if __name__ == '__main__':
    main()
