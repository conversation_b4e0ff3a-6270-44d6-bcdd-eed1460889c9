#!/usr/bin/env python3
"""
Performance Analyzer Tool for OT-O2x Codebase
Scans C# source files for performance-critical object allocation patterns
"""

import os
import re
import json
import argparse
from pathlib import Path
from typing import List, Dict, Tuple, Set
from dataclasses import dataclass, asdict
from collections import defaultdict
from datetime import datetime

@dataclass
class PerformanceIssue:
    file_path: str
    line_number: int
    line_content: str
    issue_type: str
    severity: str
    description: str
    suggestion: str

class PerformanceAnalyzer:
    def __init__(self, root_path: str):
        self.root_path = Path(root_path)
        self.issues: List[PerformanceIssue] = []

        # Critical patterns that cause performance issues
        self.critical_patterns = {
            'stopwatch_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+Stopwatch\s*\(',
                'severity': 'CRITICAL',
                'description': 'Stopwatch allocation in loop',
                'suggestion': 'Use shared Stopwatch with timestamp differences'
            },
            'task_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+Task\s*[(<]',
                'severity': 'CRITICAL',
                'description': 'Task allocation in loop',
                'suggestion': 'Use Task.Run with shared delegate or TaskFactory'
            },
            'thread_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+Thread\s*\(',
                'severity': 'CRITICAL',
                'description': 'Thread allocation in loop',
                'suggestion': 'Use ThreadPool or reuse threads'
            },
            'memorystream_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+MemoryStream\s*\(',
                'severity': 'HIGH',
                'description': 'MemoryStream allocation in loop',
                'suggestion': 'Use object pooling or reuse MemoryStream instances'
            },
            'stringbuilder_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+StringBuilder\s*\(',
                'severity': 'HIGH',
                'description': 'StringBuilder allocation in loop',
                'suggestion': 'Reuse StringBuilder instance with Clear() method'
            },
            'dictionary_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+(Dictionary|ConcurrentDictionary)\s*<',
                'severity': 'HIGH',
                'description': 'Dictionary allocation in loop',
                'suggestion': 'Move dictionary creation outside loop or use object pooling'
            },
            'list_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+List\s*<',
                'severity': 'MEDIUM',
                'description': 'List allocation in loop',
                'suggestion': 'Reuse List instance with Clear() method'
            },
            'datetime_now_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*DateTime\.Now\b',
                'severity': 'HIGH',
                'description': 'DateTime.Now in loop',
                'suggestion': 'Cache DateTime.Now outside loop'
            },
            'datetime_now_repeated': {
                'pattern': r'DateTime\.Now.*DateTime\.Now',
                'severity': 'MEDIUM',
                'description': 'Multiple DateTime.Now calls',
                'suggestion': 'Cache DateTime.Now in a variable'
            },
            'new_timespan_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*new\s+TimeSpan\s*\(',
                'severity': 'MEDIUM',
                'description': 'TimeSpan allocation in loop',
                'suggestion': 'Use TimeSpan static methods or cache instances'
            },
            'string_concat_in_loop': {
                'pattern': r'(for|while|foreach).*?{[^}]*\w+\s*\+=\s*["\']',
                'severity': 'HIGH',
                'description': 'String concatenation in loop',
                'suggestion': 'Use StringBuilder for string concatenation in loops'
            },
            'string_format_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*string\.Format\(',
                'severity': 'HIGH',
                'description': 'string.Format in loop',
                'suggestion': 'Use StringBuilder or string interpolation'
            },
            'reflection_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*(GetType|GetMethod|GetProperty|GetField)\(',
                'severity': 'HIGH',
                'description': 'Reflection in loop',
                'suggestion': 'Cache reflection results outside loop'
            },
            'concat_in_loop': {
                'pattern': r'(for|while|foreach|Parallel\.ForEach).*?{[^}]*\.Concat\(',
                'severity': 'HIGH',
                'description': 'LINQ Concat in loop',
                'suggestion': 'Use AddRange or collect items first'
            }
        }

        # Single-line patterns for simpler detection
        self.simple_patterns = {
            'new_stopwatch': {
                'pattern': r'new\s+Stopwatch\s*\(',
                'severity': 'MEDIUM',
                'description': 'Stopwatch allocation',
                'suggestion': 'Consider using shared Stopwatch for high-frequency code'
            },
            'new_exception_in_method': {
                'pattern': r'throw\s+new\s+\w*Exception\s*\(',
                'severity': 'LOW',
                'description': 'Exception allocation',
                'suggestion': 'Pre-allocate common exceptions if thrown frequently'
            },
            'tolist_unnecessary': {
                'pattern': r'\.ToList\(\)\.ToList\(\)',
                'severity': 'MEDIUM',
                'description': 'Redundant ToList() calls',
                'suggestion': 'Remove redundant ToList() calls'
            },
            'multiple_where_clauses': {
                'pattern': r'\.Where\([^)]+\)\.Where\(',
                'severity': 'HIGH',
                'description': 'Multiple LINQ Where clauses',
                'suggestion': 'Combine Where conditions with && operator'
            },
            'tolist_before_count': {
                'pattern': r'\.ToList\(\)\.Count\b',
                'severity': 'HIGH',
                'description': 'ToList().Count instead of Count()',
                'suggestion': 'Use Count() directly on IEnumerable'
            },
            'count_greater_than_zero': {
                'pattern': r'\.Count\(\)\s*>\s*0',
                'severity': 'MEDIUM',
                'description': 'Count() > 0 instead of Any()',
                'suggestion': 'Use Any() for existence checks'
            },
            'string_split_repeated': {
                'pattern': r'\.Split\([^)]+\).*\.Split\(',
                'severity': 'MEDIUM',
                'description': 'Multiple Split operations',
                'suggestion': 'Cache split result in variable'
            },
            'gettype_repeated': {
                'pattern': r'\.GetType\(\).*\.GetType\(\)',
                'severity': 'MEDIUM',
                'description': 'Multiple GetType() calls',
                'suggestion': 'Cache Type in variable'
            },
            'array_resize': {
                'pattern': r'Array\.Resize\s*\(',
                'severity': 'HIGH',
                'description': 'Array.Resize operation',
                'suggestion': 'Use List<T> or pre-allocate correct size'
            },
            'filestream_not_disposed': {
                'pattern': r'new\s+FileStream\([^)]+\)(?!.*using)',
                'severity': 'HIGH',
                'description': 'FileStream without using statement',
                'suggestion': 'Wrap in using statement or use File.ReadAllText/WriteAllText'
            },
            'file_exists_repeated': {
                'pattern': r'File\.Exists\([^)]+\).*File\.Exists\(',
                'severity': 'MEDIUM',
                'description': 'Multiple File.Exists calls',
                'suggestion': 'Cache File.Exists result'
            },
            'boxing_tostring': {
                'pattern': r'(int|long|double|float|decimal).*\.ToString\(\)',
                'severity': 'LOW',
                'description': 'Potential boxing with ToString()',
                'suggestion': 'Use ToString() with format or cache string representations'
            },
            'linq_orderby_multiple': {
                'pattern': r'\.OrderBy\([^)]+\)\.OrderBy\(',
                'severity': 'MEDIUM',
                'description': 'Multiple OrderBy calls',
                'suggestion': 'Use ThenBy for secondary sorting'
            },
            'select_tolist': {
                'pattern': r'\.Select\([^)]+\)\.ToList\(\)\.Select\(',
                'severity': 'HIGH',
                'description': 'Select().ToList().Select() chain',
                'suggestion': 'Combine Select operations or avoid intermediate ToList()'
            }
        }

    def scan_file(self, file_path: Path) -> None:
        """Scan a single C# file for performance issues"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                lines = content.split('\n')

            # Check for complex patterns (multi-line)
            self._check_complex_patterns(file_path, content, lines)

            # Check for simple patterns (single-line)
            self._check_simple_patterns(file_path, lines)

        except Exception as e:
            print(f"Error scanning {file_path}: {e}")

    def _check_complex_patterns(self, file_path: Path, content: str, lines: List[str]) -> None:
        """Check for complex multi-line patterns"""
        for pattern_name, pattern_info in self.critical_patterns.items():
            matches = re.finditer(pattern_info['pattern'], content, re.MULTILINE | re.DOTALL)
            for match in matches:
                # Find line number
                line_num = content[:match.start()].count('\n') + 1
                line_content = lines[line_num - 1].strip() if line_num <= len(lines) else ""

                self.issues.append(PerformanceIssue(
                    file_path=str(file_path.relative_to(self.root_path)),
                    line_number=line_num,
                    line_content=line_content,
                    issue_type=pattern_name,
                    severity=pattern_info['severity'],
                    description=pattern_info['description'],
                    suggestion=pattern_info['suggestion']
                ))

    def _check_simple_patterns(self, file_path: Path, lines: List[str]) -> None:
        """Check for simple single-line patterns"""
        for line_num, line in enumerate(lines, 1):
            for pattern_name, pattern_info in self.simple_patterns.items():
                if re.search(pattern_info['pattern'], line):
                    self.issues.append(PerformanceIssue(
                        file_path=str(file_path.relative_to(self.root_path)),
                        line_number=line_num,
                        line_content=line.strip(),
                        issue_type=pattern_name,
                        severity=pattern_info['severity'],
                        description=pattern_info['description'],
                        suggestion=pattern_info['suggestion']
                    ))

    def scan_directory(self, extensions: Set[str] = {'.cs'}) -> None:
        """Scan all files in directory with given extensions"""
        for file_path in self.root_path.rglob('*'):
            if file_path.is_file() and file_path.suffix in extensions:
                # Skip certain directories
                if any(skip in str(file_path) for skip in ['bin', 'obj', 'packages', '.git', 'node_modules']):
                    continue
                self.scan_file(file_path)

    def generate_report(self) -> Dict:
        """Generate comprehensive performance report"""
        # Group issues by severity
        by_severity = defaultdict(list)
        by_file = defaultdict(list)
        by_type = defaultdict(list)

        for issue in self.issues:
            by_severity[issue.severity].append(issue)
            by_file[issue.file_path].append(issue)
            by_type[issue.issue_type].append(issue)

        # Calculate statistics
        total_issues = len(self.issues)
        critical_count = len(by_severity['CRITICAL'])
        high_count = len(by_severity['HIGH'])
        medium_count = len(by_severity['MEDIUM'])
        low_count = len(by_severity['LOW'])

        # Find most problematic files
        most_problematic = sorted(by_file.items(), key=lambda x: len(x[1]), reverse=True)[:10]

        return {
            'summary': {
                'total_issues': total_issues,
                'critical_issues': critical_count,
                'high_issues': high_count,
                'medium_issues': medium_count,
                'low_issues': low_count,
                'files_scanned': len(by_file),
                'most_problematic_files': [
                    {'file': file, 'issue_count': len(issues)}
                    for file, issues in most_problematic
                ]
            },
            'issues_by_severity': {
                severity: [asdict(issue) for issue in issues]
                for severity, issues in by_severity.items()
            },
            'issues_by_type': {
                issue_type: len(issues)
                for issue_type, issues in by_type.items()
            }
        }

    def save_report(self, output_file: str) -> None:
        """Save report to JSON file"""
        report = self.generate_report()
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

    def generate_markdown_report(self) -> str:
        """Generate concise markdown report"""
        report = self.generate_report()
        summary = report['summary']

        md = f"""# 🔍 Performance Analysis Report

## 📊 Executive Summary
- **Total Issues**: {summary['total_issues']}
- **Critical**: {summary['critical_issues']} 🚨
- **High Priority**: {summary['high_issues']} ⚠️
- **Files Scanned**: {summary['files_scanned']}

## 🚨 Critical Issues (Immediate Action Required)
"""

        # Critical issues by type
        critical_types = {}
        high_types = {}
        for issue in self.issues:
            if issue.severity == 'CRITICAL':
                critical_types[issue.issue_type] = critical_types.get(issue.issue_type, 0) + 1
            elif issue.severity == 'HIGH':
                high_types[issue.issue_type] = high_types.get(issue.issue_type, 0) + 1

        if critical_types:
            for issue_type, count in sorted(critical_types.items(), key=lambda x: x[1], reverse=True):
                md += f"- **{issue_type.replace('_', ' ').title()}**: {count} occurrences\n"
        else:
            md += "- No critical issues found\n"

        md += f"""
## ⚠️ High Priority Issues
"""
        for issue_type, count in sorted(high_types.items(), key=lambda x: x[1], reverse=True)[:5]:
            md += f"- **{issue_type.replace('_', ' ').title()}**: {count} occurrences\n"

        # Critical files
        critical_files = self.get_critical_files()
        if critical_files:
            md += f"""
## 🔥 Critical Files ({len(critical_files)})
"""
            for file in critical_files[:10]:
                md += f"- `{file}`\n"

        # Hot paths
        hotpaths = self.get_hotpath_indicators()
        if hotpaths:
            md += f"""
## 🎯 Hot Paths ({len(hotpaths)} files with 3+ performance indicators)
"""
            for file, issues in sorted(hotpaths.items(), key=lambda x: len(x[1]), reverse=True)[:5]:
                md += f"- `{file}` ({len(issues)} indicators)\n"

        # Top problematic files
        md += f"""
## 📈 Most Problematic Files
"""
        for i, file_info in enumerate(summary['most_problematic_files'][:5], 1):
            md += f"{i}. `{file_info['file']}` ({file_info['issue_count']} issues)\n"

        # Action plan
        md += f"""
## 🎯 Immediate Action Plan

### Phase 1: Critical (Today)
"""
        if critical_types:
            for issue_type, count in list(sorted(critical_types.items(), key=lambda x: x[1], reverse=True))[:3]:
                suggestion = self._get_suggestion_for_type(issue_type)
                md += f"- Fix **{count}** instances of `{issue_type}`\n  - {suggestion}\n"

        md += f"""
### Phase 2: High Priority (This Week)
"""
        for issue_type, count in list(sorted(high_types.items(), key=lambda x: x[1], reverse=True))[:3]:
            suggestion = self._get_suggestion_for_type(issue_type)
            md += f"- Address **{count}** instances of `{issue_type}`\n  - {suggestion}\n"

        md += f"""
## 💡 Key Recommendations
1. **Implement Object Pooling** for frequently allocated objects
2. **Cache DateTime.Now** in hot paths
3. **Use Shared Stopwatch** for timing operations
4. **Optimize LINQ chains** to reduce intermediate allocations
5. **Monitor GC pressure** after fixes

---
*Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        return md

    def _get_suggestion_for_type(self, issue_type: str) -> str:
        """Get suggestion for specific issue type"""
        suggestions = {
            'stopwatch_in_loop': 'Use shared Stopwatch with timestamp differences',
            'task_in_loop': 'Use Task.Run with shared delegate or TaskFactory',
            'thread_in_loop': 'Use ThreadPool or reuse threads',
            'dictionary_in_loop': 'Move dictionary creation outside loop or use object pooling',
            'datetime_now_in_loop': 'Cache DateTime.Now outside loop',
            'memorystream_in_loop': 'Use object pooling or reuse MemoryStream instances',
            'stringbuilder_in_loop': 'Reuse StringBuilder instance with Clear() method',
            'string_concat_in_loop': 'Use StringBuilder for string concatenation in loops',
            'list_in_loop': 'Reuse List instance with Clear() method',
            'tolist_before_count': 'Use Count() directly on IEnumerable',
            'multiple_where_clauses': 'Combine Where conditions with && operator'
        }
        return suggestions.get(issue_type, 'Review and optimize allocation pattern')

    def save_markdown_report(self, output_file: str) -> None:
        """Save markdown report to file"""
        md_content = self.generate_markdown_report()
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(md_content)

    def generate_detailed_report(self) -> str:
        """Generate detailed report with code examples and specific fixes"""
        report = self.generate_report()

        md = f"""# 🔧 Performance Issues - Detailed Technical Report

## 📊 Analysis Summary
- **Total Issues Found**: {report['summary']['total_issues']}
- **Critical Issues**: {report['summary']['critical_issues']} 🚨
- **High Priority**: {report['summary']['high_issues']} ⚠️
- **Files Analyzed**: {report['summary']['files_scanned']}

---

"""

        # Group issues by type for detailed analysis
        issues_by_type = {}
        for issue in self.issues:
            if issue.issue_type not in issues_by_type:
                issues_by_type[issue.issue_type] = []
            issues_by_type[issue.issue_type].append(issue)

        # Sort by severity and count
        severity_order = {'CRITICAL': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3}
        sorted_types = sorted(issues_by_type.items(),
                            key=lambda x: (severity_order.get(x[1][0].severity, 4), -len(x[1])))

        for issue_type, issues in sorted_types:
            if len(issues) == 0:
                continue

            severity = issues[0].severity
            severity_icon = "🚨" if severity == 'CRITICAL' else "⚠️" if severity == 'HIGH' else "🔶" if severity == 'MEDIUM' else "ℹ️"

            md += f"""## {severity_icon} {issue_type.replace('_', ' ').title()} ({len(issues)} occurrences)

**Severity**: {severity}
**Impact**: {self._get_impact_description(issue_type)}

### 🔍 Problem Description
{self._get_problem_description(issue_type)}

### ❌ Problematic Code Pattern
```csharp
{self._get_bad_example(issue_type)}
```

### ✅ Optimized Solution
```csharp
{self._get_good_example(issue_type)}
```

### 📍 Found in Files:
"""

            # Show up to 10 files with this issue
            file_counts = {}
            for issue in issues:
                file_counts[issue.file_path] = file_counts.get(issue.file_path, 0) + 1

            sorted_files = sorted(file_counts.items(), key=lambda x: x[1], reverse=True)
            for file_path, count in sorted_files[:10]:
                md += f"- `{file_path}` ({count} instances)\n"

            if len(sorted_files) > 10:
                md += f"- ... and {len(sorted_files) - 10} more files\n"

            md += f"""
### 🎯 Action Required
{self._get_action_required(issue_type, len(issues))}

---

"""

        # Add implementation guide
        md += self._get_implementation_guide()

        return md

    def _get_impact_description(self, issue_type: str) -> str:
        """Get impact description for issue type"""
        impacts = {
            'stopwatch_in_loop': 'Massive object allocations in hot paths, potential memory leaks',
            'task_in_loop': 'Thread pool exhaustion, system instability',
            'thread_in_loop': 'Resource exhaustion, potential deadlocks',
            'dictionary_in_loop': 'High memory pressure, frequent GC collections',
            'datetime_now_in_loop': 'Expensive system calls in performance-critical code',
            'memorystream_in_loop': 'Memory fragmentation, increased allocation rate',
            'stringbuilder_in_loop': 'Unnecessary object creation, string handling overhead',
            'string_concat_in_loop': 'Quadratic memory growth, performance degradation',
            'list_in_loop': 'Frequent allocations, memory pressure',
            'tolist_before_count': 'Unnecessary enumeration and memory allocation',
            'multiple_where_clauses': 'Multiple LINQ iterations, performance overhead'
        }
        return impacts.get(issue_type, 'Performance degradation due to inefficient patterns')

    def _get_problem_description(self, issue_type: str) -> str:
        """Get detailed problem description"""
        descriptions = {
            'stopwatch_in_loop': 'Creating new Stopwatch instances inside loops causes excessive object allocations. In high-frequency scenarios, this can create thousands of objects per second, leading to memory pressure and frequent garbage collection.',
            'task_in_loop': 'Creating Task instances in loops can exhaust the thread pool and cause system instability. Each Task creation has overhead and can lead to resource contention.',
            'dictionary_in_loop': 'Allocating Dictionary or ConcurrentDictionary instances for each iteration creates significant memory pressure. These collections have internal arrays that contribute to heap fragmentation.',
            'datetime_now_in_loop': 'DateTime.Now involves a system call to get the current time. Calling it repeatedly in loops adds unnecessary overhead, especially in high-frequency processing.',
            'memorystream_in_loop': 'MemoryStream allocations in loops create memory pressure and fragmentation. Each stream allocates internal buffers that contribute to GC pressure.',
            'stringbuilder_in_loop': 'Creating StringBuilder instances for each iteration defeats the purpose of using StringBuilder. The overhead of object creation outweighs the benefits.',
            'string_concat_in_loop': 'String concatenation with += creates new string objects for each operation, leading to quadratic memory growth and performance degradation.',
            'tolist_before_count': 'Calling ToList() before Count() forces enumeration of the entire sequence and allocates a List, when Count() can often be determined without enumeration.',
            'multiple_where_clauses': 'Chaining multiple Where() clauses creates multiple enumerators and can iterate the sequence multiple times, reducing performance.'
        }
        return descriptions.get(issue_type, 'This pattern creates unnecessary overhead and should be optimized.')

    def _get_bad_example(self, issue_type: str) -> str:
        """Get bad code example for issue type"""
        examples = {
            'stopwatch_in_loop': '''// ❌ Creates new Stopwatch for each iteration
foreach (var item in items) {
    var sw = new Stopwatch();
    sw.Start();
    ProcessItem(item);
    sw.Stop();
    LogTime(sw.ElapsedMilliseconds);
}''',
            'task_in_loop': '''// ❌ Creates new Task for each item
foreach (var item in items) {
    var task = new Task(() => ProcessItem(item));
    task.Start();
    await task;
}''',
            'dictionary_in_loop': '''// ❌ New dictionary for each batch
foreach (var batch in batches) {
    var cache = new Dictionary<string, object>();
    ProcessBatch(batch, cache);
}''',
            'datetime_now_in_loop': '''// ❌ System call for each item
foreach (var item in items) {
    item.ProcessedAt = DateTime.Now;
    ProcessItem(item);
}''',
            'memorystream_in_loop': '''// ❌ New stream for each serialization
foreach (var obj in objects) {
    using var ms = new MemoryStream();
    SerializeObject(obj, ms);
}''',
            'string_concat_in_loop': '''// ❌ Quadratic string growth
string result = "";
foreach (var item in items) {
    result += item.ToString() + ", ";
}'''
        }
        return examples.get(issue_type, '// Problematic pattern detected')

    def _get_good_example(self, issue_type: str) -> str:
        """Get good code example for issue type"""
        examples = {
            'stopwatch_in_loop': '''// ✅ Shared Stopwatch with timestamp differences
private static readonly Stopwatch _sharedStopwatch = Stopwatch.StartNew();

foreach (var item in items) {
    long startTicks = _sharedStopwatch.ElapsedTicks;
    ProcessItem(item);
    long elapsedTicks = _sharedStopwatch.ElapsedTicks - startTicks;
    long elapsedMs = (elapsedTicks * 1000) / Stopwatch.Frequency;
    LogTime(elapsedMs);
}''',
            'task_in_loop': '''// ✅ Use Task.Run or parallel processing
var tasks = items.Select(item => Task.Run(() => ProcessItem(item)));
await Task.WhenAll(tasks);

// Or use Parallel.ForEach for CPU-bound work
Parallel.ForEach(items, item => ProcessItem(item));''',
            'dictionary_in_loop': '''// ✅ Object pooling or reuse
private static readonly ObjectPool<Dictionary<string, object>> _dictPool =
    new DefaultObjectPool<Dictionary<string, object>>(new DictionaryPooledObjectPolicy());

foreach (var batch in batches) {
    var cache = _dictPool.Get();
    try {
        cache.Clear();
        ProcessBatch(batch, cache);
    } finally {
        _dictPool.Return(cache);
    }
}''',
            'datetime_now_in_loop': '''// ✅ Cache DateTime.Now outside loop
var now = DateTime.Now;
foreach (var item in items) {
    item.ProcessedAt = now;
    ProcessItem(item);
}''',
            'memorystream_in_loop': '''// ✅ Object pooling for MemoryStream
private static readonly ObjectPool<MemoryStream> _streamPool =
    new DefaultObjectPool<MemoryStream>(new MemoryStreamPooledObjectPolicy());

foreach (var obj in objects) {
    var ms = _streamPool.Get();
    try {
        ms.SetLength(0);
        SerializeObject(obj, ms);
    } finally {
        _streamPool.Return(ms);
    }
}''',
            'string_concat_in_loop': '''// ✅ StringBuilder with pre-sizing
var sb = new StringBuilder(items.Count * 20); // Pre-size if possible
foreach (var item in items) {
    sb.Append(item.ToString()).Append(", ");
}
string result = sb.ToString();'''
        }
        return examples.get(issue_type, '// Optimized pattern')

    def _get_action_required(self, issue_type: str, count: int) -> str:
        """Get specific action required for issue type"""
        if issue_type in ['stopwatch_in_loop', 'task_in_loop', 'thread_in_loop']:
            return f"**CRITICAL**: Fix immediately. {count} instances can cause system instability."
        elif issue_type in ['dictionary_in_loop', 'datetime_now_in_loop', 'memorystream_in_loop']:
            return f"**HIGH PRIORITY**: Implement object pooling or caching. {count} instances causing memory pressure."
        else:
            return f"**MEDIUM PRIORITY**: Optimize when possible. {count} instances affecting performance."

    def _get_implementation_guide(self) -> str:
        """Get implementation guide"""
        return """## 🛠️ Implementation Guide

### 1. Object Pooling Setup
Add to your `Startup.cs` or DI container:

```csharp
// Register object pools
services.AddSingleton<ObjectPoolProvider, DefaultObjectPoolProvider>();
services.AddSingleton(provider => {
    var poolProvider = provider.GetService<ObjectPoolProvider>();
    return poolProvider.Create(new MemoryStreamPooledObjectPolicy());
});
```

### 2. Shared Stopwatch Pattern
```csharp
public class PerformanceTimer
{
    private static readonly Stopwatch _sharedStopwatch = Stopwatch.StartNew();

    public static long GetElapsedMilliseconds(long startTicks)
    {
        return (_sharedStopwatch.ElapsedTicks - startTicks) * 1000 / Stopwatch.Frequency;
    }

    public static long GetCurrentTicks() => _sharedStopwatch.ElapsedTicks;
}
```

### 3. DateTime Caching Service
```csharp
public class TimeProvider
{
    private DateTime _cachedNow = DateTime.Now;
    private readonly Timer _timer;

    public TimeProvider()
    {
        _timer = new Timer(UpdateTime, null, 0, 100); // Update every 100ms
    }

    private void UpdateTime(object state) => _cachedNow = DateTime.Now;
    public DateTime Now => _cachedNow;
}
```

### 4. Monitoring Performance Improvements
```csharp
// Add performance counters
private static readonly Counter _allocationsCounter =
    Metrics.CreateCounter("allocations_total", "Total object allocations");

private static readonly Histogram _gcDuration =
    Metrics.CreateHistogram("gc_duration_seconds", "GC collection duration");
```

### 5. Testing Strategy
1. **Baseline Measurement**: Record current performance metrics
2. **Incremental Fixes**: Fix one issue type at a time
3. **Performance Testing**: Measure impact after each fix
4. **Load Testing**: Verify improvements under realistic load
5. **Monitoring**: Set up alerts for performance regressions

---
*For questions or assistance with implementation, consult the development team lead.*
"""

    def save_detailed_report(self, output_file: str) -> None:
        """Save detailed report to file"""
        detailed_content = self.generate_detailed_report()
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(detailed_content)

    def get_critical_files(self) -> List[str]:
        """Get list of files with critical performance issues"""
        critical_files = set()
        for issue in self.issues:
            if issue.severity == 'CRITICAL':
                critical_files.add(issue.file_path)
        return sorted(list(critical_files))

    def get_hotpath_indicators(self) -> Dict[str, List[PerformanceIssue]]:
        """Identify potential hot paths based on issue patterns"""
        hotpaths = defaultdict(list)

        # Files with multiple loop-related issues are likely hot paths
        for issue in self.issues:
            if any(keyword in issue.issue_type for keyword in ['_in_loop', 'repeated', 'multiple']):
                hotpaths[issue.file_path].append(issue)

        # Filter to files with 3+ hot path indicators
        return {file: issues for file, issues in hotpaths.items() if len(issues) >= 3}

    def print_summary(self) -> None:
        """Print summary to console"""
        report = self.generate_report()
        summary = report['summary']

        print("🔍 PERFORMANCE ANALYSIS REPORT")
        print("=" * 50)
        print(f"📊 Total Issues Found: {summary['total_issues']}")
        print(f"🚨 Critical: {summary['critical_issues']}")
        print(f"⚠️  High: {summary['high_issues']}")
        print(f"🔶 Medium: {summary['medium_issues']}")
        print(f"ℹ️  Low: {summary['low_issues']}")
        print(f"📁 Files Scanned: {summary['files_scanned']}")

        # Show critical files
        critical_files = self.get_critical_files()
        if critical_files:
            print(f"\n🚨 CRITICAL FILES ({len(critical_files)}):")
            for file in critical_files[:10]:  # Show top 10
                print(f"  • {file}")

        # Show potential hot paths
        hotpaths = self.get_hotpath_indicators()
        if hotpaths:
            print(f"\n🔥 POTENTIAL HOT PATHS ({len(hotpaths)}):")
            for file, issues in sorted(hotpaths.items(), key=lambda x: len(x[1]), reverse=True)[:5]:
                print(f"  • {file} ({len(issues)} indicators)")

        print("\n🏆 TOP 5 MOST PROBLEMATIC FILES:")
        for i, file_info in enumerate(summary['most_problematic_files'][:5], 1):
            print(f"{i}. {file_info['file']} ({file_info['issue_count']} issues)")

        print("\n📈 ISSUES BY TYPE:")
        for issue_type, count in sorted(report['issues_by_type'].items(), key=lambda x: x[1], reverse=True):
            severity_icon = "🚨" if any(i.severity == 'CRITICAL' for i in self.issues if i.issue_type == issue_type) else \
                           "⚠️" if any(i.severity == 'HIGH' for i in self.issues if i.issue_type == issue_type) else "🔶"
            print(f"  {severity_icon} {issue_type}: {count}")

    def generate_refactoring_suggestions(self) -> Dict[str, str]:
        """Generate specific refactoring suggestions for common patterns"""
        suggestions = {
            'stopwatch_in_loop': '''
// ❌ BEFORE (Performance Issue)
for (int i = 0; i < items.Count; i++) {
    Stopwatch sw = new Stopwatch();
    sw.Start();
    // ... processing ...
    sw.Stop();
    metrics.Set(sw.ElapsedMilliseconds);
}

// ✅ AFTER (Optimized)
private static readonly Stopwatch _sharedStopwatch = Stopwatch.StartNew();

for (int i = 0; i < items.Count; i++) {
    long startTicks = _sharedStopwatch.ElapsedTicks;
    // ... processing ...
    long elapsedTicks = _sharedStopwatch.ElapsedTicks - startTicks;
    long elapsedMs = (elapsedTicks * 1000) / Stopwatch.Frequency;
    metrics.Set(elapsedMs);
}''',

            'memorystream_in_loop': '''
// ❌ BEFORE (Performance Issue)
foreach (var item in items) {
    using var ms = new MemoryStream();
    // ... write to stream ...
}

// ✅ AFTER (Optimized with Object Pool)
private static readonly ObjectPool<MemoryStream> _streamPool =
    new DefaultObjectPool<MemoryStream>(new MemoryStreamPooledObjectPolicy());

foreach (var item in items) {
    var ms = _streamPool.Get();
    try {
        ms.SetLength(0); // Reset stream
        // ... write to stream ...
    } finally {
        _streamPool.Return(ms);
    }
}''',

            'string_concat_in_loop': '''
// ❌ BEFORE (Performance Issue)
string result = "";
foreach (var item in items) {
    result += item.ToString() + ", ";
}

// ✅ AFTER (Optimized)
var sb = new StringBuilder(items.Count * 10); // Pre-size if possible
foreach (var item in items) {
    sb.Append(item.ToString()).Append(", ");
}
string result = sb.ToString();''',

            'tolist_before_count': '''
// ❌ BEFORE (Performance Issue)
if (items.Where(x => x.IsActive).ToList().Count > 0) {
    // ...
}

// ✅ AFTER (Optimized)
if (items.Where(x => x.IsActive).Any()) {
    // ...
}'''
        }
        return suggestions

def main():
    parser = argparse.ArgumentParser(description='Analyze C# codebase for performance issues')
    parser.add_argument('--root', default='.', help='Root directory to scan')
    parser.add_argument('--output', default='performance_report.json', help='Output JSON file')
    parser.add_argument('--markdown', default='performance_report.md', help='Output Markdown file')
    parser.add_argument('--detailed', default='performance_detailed_report.md', help='Detailed technical report')
    parser.add_argument('--summary-only', action='store_true', help='Print only summary')
    parser.add_argument('--show-examples', action='store_true', help='Show refactoring examples')

    args = parser.parse_args()

    analyzer = PerformanceAnalyzer(args.root)
    print(f"🔍 Scanning {args.root} for performance issues...")

    analyzer.scan_directory()
    analyzer.print_summary()

    if args.show_examples:
        print("\n💡 REFACTORING EXAMPLES:")
        print("=" * 50)
        suggestions = analyzer.generate_refactoring_suggestions()
        for pattern, example in suggestions.items():
            if any(issue.issue_type == pattern for issue in analyzer.issues):
                print(f"\n🔧 {pattern.upper().replace('_', ' ')}:")
                print(example)

    if not args.summary_only:
        analyzer.save_report(args.output)
        analyzer.save_markdown_report(args.markdown)
        analyzer.save_detailed_report(args.detailed)
        print(f"\n📄 JSON report saved to: {args.output}")
        print(f"📋 Summary report saved to: {args.markdown}")
        print(f"🔧 Detailed technical report saved to: {args.detailed}")

if __name__ == '__main__':
    main()
